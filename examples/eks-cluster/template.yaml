apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
# Template metadata. Here's your intro to what this template does.
metadata:
  name: eks-cluster
  title: Node.js App on AWS EKS Cluster
  description: Deploy a Node.js App on AWS EKS Cluster using Terraform
# The specification for how the template behaves
spec:
  # Who owns this template. Generally, it could be a team or individual
  owner: user:guest
  # The type of service this template deploys
  type: service
  # User-input parameters. Makes your templates dynamic!
  parameters:
    # Ask the user to input some basic app details
    - title: Fill in some steps
      required:
        - name
      properties:
        name:
          title: App Name
          type: string
          description: Unique name for your app
          ui:autofocus: true # This field gets auto-focused in UI
          ui:options:
            rows: 5 # Number of rows in the input area
    # Ask the user where they want to store the code
    - title: Choose a Repo location
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker # A special UI component for selecting repo URLs
          ui:options:
            allowedHosts:
              - github.com # Allowed hosts for repository
    # Parameters for setting up the EKS cluster
    - title: Basic EKS Cluster Configuration
      required:
        - clusterName
        - region
        - action
      properties:
        clusterName:
          title: Cluster Name
          type: string
          description: The name of your EKS cluster
          ui:autofocus: true
        region:
          title: AWS Region
          type: string
          description: The AWS region where the cluster will be deployed
          enum:
            - us-east-1
            - us-west-2
        action:
          title: Action
          type: string
          description: Action to perform (apply/destroy)
          enum:
            - apply
            - destroy
  # Steps that the template will execute in order
  steps:
    # Fetch the base template
    - id: fetch-base
      name: Fetch Base
      action: fetch:template
      input:
        url: ./content # Where the base content is stored
        values:
          name: ${{ parameters.name }}
    # Publish the code to a GitHub repo
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: This is ${{ parameters.name }}
        repoUrl: ${{ parameters.repoUrl }}
    # Trigger a GitHub Action to set up the EKS cluster
    - id: github-action
      name: Trigger GitHub Action
      action: github:actions:dispatch
      input:
        workflowId: manage-eks-cluster.yml # GitHub Action workflow ID
        repoUrl: 'github.com?repo=aws-eks&amp;owner=samgabrail'
        branchOrTagName: 'main' # The branch to run this action on
        workflowInputs:
          clusterName: ${{ parameters.clusterName }}
          awsRegion: ${{ parameters.region }}
          action: ${{ parameters.action }}
    # Register the new component in the Backstage catalog
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml' # Where the catalog info is stored
  # Output links to the user after the template execution
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
