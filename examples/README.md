# 🚀 Backstage Templates - AWS Terraform

Este diretório contém templates do Backstage para provisionamento de recursos AWS usando Terraform.

## 📦 Templates Disponíveis

### 🖥️ Compute
- **[eks-cluster](./eks-cluster/)** - Cluster Kubernetes gerenciado (EKS)
- **[ec2-instance](./ec2-instance/)** - Instância EC2 com Security Group

### 🗄️ Storage
- **[s3-bucket](./s3-bucket/)** - Bucket S3 com configurações de segurança

### 🗃️ Database
- **[rds-database](./rds-database/)** - Banco de dados RDS gerenciado

### 🌐 Networking
- **[vpc](./vpc/)** - VPC completa com subnets públicas/privadas
- **[security-group](./security-group/)** - Security Group customizável

## 🏗️ Estrutura dos Templates

Cada template segue a mesma estrutura:

```
template-name/
├── template.yaml          # Definição do template Backstage
└── content/              # Arquivos gerados pelo template
    ├── catalog-info.yaml # Metadados do componente
    ├── main.tf          # Recursos Terraform principais
    ├── variables.tf     # Variáveis de entrada
    ├── outputs.tf       # Outputs do Terraform
    └── [outros arquivos] # Scripts, configs específicos
```

## 🚀 Como Usar

### 1. Via Interface Web
1. Acesse o Backstage em `http://localhost:3000`
2. Vá para **Create** → **Choose a template**
3. Selecione o template desejado
4. Preencha os parâmetros
5. Clique em **Create**

### 2. Desenvolvimento Local
```bash
# Clonar o repositório gerado
git clone <repository-url>
cd <repository-name>

# Inicializar Terraform
terraform init

# Planejar mudanças
terraform plan

# Aplicar (cuidado em produção!)
terraform apply
```

## 🔧 Configuração

Os templates estão configurados em:
- `app-config.yaml` (desenvolvimento)
- `app-config.production.yaml` (produção)

```yaml
catalog:
  locations:
    - type: file
      target: ../../examples/[template-name]/template.yaml
      rules:
        - allow: [Template]
```

## 📋 Pré-requisitos

### AWS
- Conta AWS configurada
- Credenciais AWS (via AWS CLI, IAM roles, ou variáveis de ambiente)
- Permissões adequadas para criar recursos

### Terraform
- Terraform >= 1.0
- Provider AWS >= 5.0

### GitHub
- Token de acesso pessoal
- Permissões para criar repositórios

## 🔒 Segurança

Todos os templates seguem práticas de segurança:
- ✅ Encryption habilitada por padrão
- ✅ Least privilege access
- ✅ Security groups restritivos
- ✅ Backup automático (quando aplicável)
- ✅ Logging e monitoring

## 🛠️ Personalização

Para criar um novo template:

1. **Copie um template existente**:
   ```bash
   cp -r template/ meu-novo-template/
   ```

2. **Edite o template.yaml**:
   - Altere metadata (name, title, description)
   - Ajuste parâmetros conforme necessário
   - Mapeie valores para o Terraform

3. **Adapte os arquivos Terraform**:
   - `main.tf`: Recursos AWS específicos
   - `variables.tf`: Parâmetros do template
   - `outputs.tf`: Informações importantes

4. **Atualize a configuração**:
   ```yaml
   # app-config.yaml
   - type: file
     target: ../../examples/meu-novo-template/template.yaml
     rules:
       - allow: [Template]
   ```

5. **Reinicie o Backstage**:
   ```bash
   yarn dev
   ```

## 📚 Documentação

- [Guia Completo](../terraform-backstage-guide.md)
- [Catálogo de Referência](../terraform-catalog-reference.md)
- [Backstage Templates](https://backstage.io/docs/features/software-templates/)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws)

## 🐛 Troubleshooting

### Template não aparece
1. Verificar configuração em `app-config.yaml`
2. Restart do Backstage
3. Refresh no Catalog → Locations

### Erro no Terraform
1. Verificar credenciais AWS
2. Validar permissões IAM
3. Conferir sintaxe HCL

### Logs úteis
```bash
# Backend logs
tail -f packages/backend/dist/bundle.log

# Terraform logs
export TF_LOG=DEBUG
terraform plan
```

## 🤝 Contribuição

Para adicionar novos templates:
1. Fork do repositório
2. Crie o template seguindo a estrutura
3. Teste localmente
4. Abra um Pull Request

---

**Mantido por**: DevOps Team  
**Última atualização**: $(date)
