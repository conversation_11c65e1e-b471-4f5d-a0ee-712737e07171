apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: security-group-terraform
  title: AWS Security Group with Terraform
  description: Create a customizable AWS Security Group with predefined rules using Terraform
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Basic Information
      required:
        - name
        - description
      properties:
        name:
          title: Security Group Name
          type: string
          description: Name for your security group
          ui:autofocus: true
        description:
          title: Description
          type: string
          description: Purpose of this security group
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
    - title: Security Group Configuration
      required:
        - region
        - sgType
      properties:
        region:
          title: AWS Region
          type: string
          description: AWS region for the security group
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - ap-southeast-1
          default: us-east-1
        sgType:
          title: Security Group Type
          type: string
          description: Predefined security group template
          enum:
            - web-server
            - database
            - load-balancer
            - application-server
            - custom
          default: web-server
        allowSSH:
          title: Allow SSH Access
          type: boolean
          description: Allow SSH (port 22) access
          default: true
        allowHTTP:
          title: Allow HTTP Access
          type: boolean
          description: Allow HTTP (port 80) access
          default: true
        allowHTTPS:
          title: Allow HTTPS Access
          type: boolean
          description: Allow HTTPS (port 443) access
          default: true
        customPorts:
          title: Custom Ports
          type: string
          description: Additional ports to open (comma-separated, e.g., 8080,9000)
          default: ""
  steps:
    - id: fetch-base
      name: Fetch Base
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          region: ${{ parameters.region }}
          sgType: ${{ parameters.sgType }}
          allowSSH: ${{ parameters.allowSSH }}
          allowHTTP: ${{ parameters.allowHTTP }}
          allowHTTPS: ${{ parameters.allowHTTPS }}
          customPorts: ${{ parameters.customPorts }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
