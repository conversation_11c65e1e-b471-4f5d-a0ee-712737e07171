# 🔧 Configuração AWS CLI para LocalStack

## 🎯 **Credenciais Padrão LocalStack**

Todos os templates LocalStack usam as credenciais padrão:
- **Access Key**: `sample`
- **Secret Key**: `sample`
- **Region**: `us-east-1` (pad<PERSON><PERSON>)
- **Endpoint**: `http://localhost:4566`

## ⚙️ **Configuração AWS CLI**

### **Método 1: Perfil LocalStack (Recomendado)**

```bash
# Configurar perfil específico para LocalStack
aws configure set aws_access_key_id sample --profile localstack
aws configure set aws_secret_access_key sample --profile localstack
aws configure set region us-east-1 --profile localstack

# Verificar configuração
aws configure list --profile localstack
```

### **Método 2: Variáveis de Ambiente**

```bash
# Configurar variáveis de ambiente
export AWS_ACCESS_KEY_ID=sample
export AWS_SECRET_ACCESS_KEY=sample
export AWS_DEFAULT_REGION=us-east-1

# Verificar
echo $AWS_ACCESS_KEY_ID
```

### **Método 3: Arquivo de Credenciais Manual**

```bash
# Criar diretório AWS
mkdir -p ~/.aws

# Criar arquivo de credenciais
cat > ~/.aws/credentials << EOF
[default]
aws_access_key_id = sample
aws_secret_access_key = sample

[localstack]
aws_access_key_id = sample
aws_secret_access_key = sample
EOF

# Criar arquivo de configuração
cat > ~/.aws/config << EOF
[default]
region = us-east-1

[profile localstack]
region = us-east-1
EOF
```

## 🚀 **Aliases Úteis**

### **Criar Aliases**

```bash
# Adicionar ao ~/.bashrc ou ~/.zshrc
echo 'alias awslocal="aws --endpoint-url=http://localhost:4566 --profile localstack"' >> ~/.bashrc
echo 'alias awsl="aws --endpoint-url=http://localhost:4566 --profile localstack"' >> ~/.bashrc

# Recarregar shell
source ~/.bashrc
```

### **Usar Aliases**

```bash
# Em vez de:
aws --endpoint-url=http://localhost:4566 --profile localstack s3 ls

# Use:
awslocal s3 ls
# ou
awsl s3 ls
```

## 🧪 **Comandos de Teste**

### **Verificar Conectividade**

```bash
# Health check LocalStack
curl http://localhost:4566/health

# Teste AWS CLI
awslocal sts get-caller-identity

# Listar serviços disponíveis
curl http://localhost:4566/_localstack/health
```

### **Testes por Serviço**

#### **S3**
```bash
# Listar buckets
awslocal s3 ls

# Criar bucket teste
awslocal s3 mb s3://teste-bucket

# Upload arquivo
echo "Hello LocalStack" > test.txt
awslocal s3 cp test.txt s3://teste-bucket/

# Download arquivo
awslocal s3 cp s3://teste-bucket/test.txt downloaded.txt

# Remover bucket
awslocal s3 rb s3://teste-bucket --force
```

#### **EC2**
```bash
# Listar instâncias
awslocal ec2 describe-instances

# Listar AMIs
awslocal ec2 describe-images

# Listar security groups
awslocal ec2 describe-security-groups

# Listar key pairs
awslocal ec2 describe-key-pairs

# Listar VPCs
awslocal ec2 describe-vpcs
```

#### **DynamoDB**
```bash
# Listar tabelas
awslocal dynamodb list-tables

# Criar tabela teste
awslocal dynamodb create-table \
  --table-name TestTable \
  --attribute-definitions AttributeName=id,AttributeType=S \
  --key-schema AttributeName=id,KeyType=HASH \
  --billing-mode PAY_PER_REQUEST

# Inserir item
awslocal dynamodb put-item \
  --table-name TestTable \
  --item '{"id":{"S":"test-id"},"name":{"S":"Test Item"}}'

# Buscar item
awslocal dynamodb get-item \
  --table-name TestTable \
  --key '{"id":{"S":"test-id"}}'

# Scan tabela
awslocal dynamodb scan --table-name TestTable

# Deletar tabela
awslocal dynamodb delete-table --table-name TestTable
```

## 🔧 **Script de Setup Automático**

### **Criar Script de Configuração**

```bash
# Criar script setup-localstack-aws.sh
cat > setup-localstack-aws.sh << 'EOF'
#!/bin/bash

echo "🔧 Configurando AWS CLI para LocalStack..."

# Configurar perfil LocalStack
aws configure set aws_access_key_id sample --profile localstack
aws configure set aws_secret_access_key sample --profile localstack
aws configure set region us-east-1 --profile localstack

# Criar aliases
if ! grep -q "awslocal" ~/.bashrc; then
    echo 'alias awslocal="aws --endpoint-url=http://localhost:4566 --profile localstack"' >> ~/.bashrc
    echo 'alias awsl="aws --endpoint-url=http://localhost:4566 --profile localstack"' >> ~/.bashrc
    echo "✅ Aliases adicionados ao ~/.bashrc"
fi

# Verificar LocalStack
if curl -s http://localhost:4566/health > /dev/null; then
    echo "✅ LocalStack está rodando"
else
    echo "⚠️  LocalStack não está rodando. Execute:"
    echo "   docker run --rm -it -p 4566:4566 localstack/localstack"
fi

# Testar configuração
echo "🧪 Testando configuração..."
aws --endpoint-url=http://localhost:4566 --profile localstack sts get-caller-identity

echo "🎉 Configuração concluída!"
echo "💡 Use 'awslocal' ou 'awsl' para comandos LocalStack"
echo "📋 Exemplo: awslocal s3 ls"

EOF

# Tornar executável
chmod +x setup-localstack-aws.sh

# Executar
./setup-localstack-aws.sh
```

## 📋 **Verificação de Configuração**

### **Checklist de Verificação**

```bash
# 1. Verificar perfil LocalStack
aws configure list --profile localstack

# 2. Verificar conectividade
curl http://localhost:4566/health

# 3. Testar AWS CLI
awslocal sts get-caller-identity

# 4. Verificar aliases
which awslocal

# 5. Testar serviços
awslocal s3 ls
awslocal ec2 describe-instances
awslocal dynamodb list-tables
```

### **Troubleshooting**

#### **Erro: "Unable to locate credentials"**
```bash
# Verificar configuração
aws configure list --profile localstack

# Reconfigurar se necessário
aws configure set aws_access_key_id sample --profile localstack
aws configure set aws_secret_access_key sample --profile localstack
```

#### **Erro: "Could not connect to the endpoint"**
```bash
# Verificar se LocalStack está rodando
curl http://localhost:4566/health

# Iniciar LocalStack se necessário
docker run --rm -it -p 4566:4566 localstack/localstack
```

#### **Erro: "Invalid endpoint"**
```bash
# Verificar endpoint
echo "Endpoint: http://localhost:4566"

# Testar conectividade
curl http://localhost:4566/_localstack/health
```

## 🎯 **Comandos Essenciais**

### **Comandos Diários**

```bash
# Iniciar LocalStack
docker run --rm -it -p 4566:4566 localstack/localstack

# Verificar status
curl http://localhost:4566/health

# Listar recursos
awslocal s3 ls
awslocal ec2 describe-instances
awslocal dynamodb list-tables

# Parar LocalStack
docker stop $(docker ps -q --filter ancestor=localstack/localstack)
```

### **Comandos de Debug**

```bash
# Ver logs LocalStack
docker logs $(docker ps -q --filter ancestor=localstack/localstack)

# Debug AWS CLI
aws --endpoint-url=http://localhost:4566 --profile localstack --debug s3 ls

# Verificar configuração
aws configure list --profile localstack
env | grep AWS
```

## 📚 **Recursos Úteis**

- **LocalStack Health**: `http://localhost:4566/health`
- **LocalStack Dashboard**: `http://localhost:4566/_localstack/cockpit`
- **Service Status**: `http://localhost:4566/_localstack/health`

## 🎉 **Resumo**

**✅ Credenciais configuradas**: `sample/sample`  
**✅ Perfil criado**: `localstack`  
**✅ Aliases disponíveis**: `awslocal`, `awsl`  
**✅ Endpoint**: `http://localhost:4566`  

Agora você pode usar `awslocal` para todos os comandos AWS com LocalStack! 🚀

---

**Configuração**: AWS CLI + LocalStack  
**Credenciais**: sample/sample  
**Endpoint**: localhost:4566
