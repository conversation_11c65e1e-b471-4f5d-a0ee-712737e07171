#!/bin/bash

# Script para iniciar Backstage com configurações corretas para Node.js 20+

echo "🚀 Iniciando Backstage com configurações Node.js 20+"

# Configurar NODE_OPTIONS para Node.js 20+
export NODE_OPTIONS="--no-node-snapshot"

# Verificar versão do Node.js
echo "📋 Versão do Node.js: $(node --version)"

# Verificar se LocalStack está rodando (opcional)
if curl -s http://localhost:4566/health > /dev/null 2>&1; then
    echo "✅ LocalStack está rodando em http://localhost:4566"
else
    echo "⚠️  LocalStack não detectado. Para usar templates LocalStack, execute:"
    echo "   docker run --rm -it -p 4566:4566 localstack/localstack"
fi

echo "🔧 NODE_OPTIONS configurado: $NODE_OPTIONS"
echo "🎯 Iniciando Backstage..."

# Iniciar Backstage
yarn dev
