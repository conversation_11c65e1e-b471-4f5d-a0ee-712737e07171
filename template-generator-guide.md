# 🚀 Guia: Como Criar Qualquer Recurso AWS com Backstage + Terraform

## 🎯 Template Universal

### **1. Template.yaml Base**

```yaml
apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-terraform-[RECURSO]
  title: [TÍTULO DO RECURSO] - Terraform
  description: Cria [DESCRIÇÃO DO RECURSO] na AWS usando Terraform
  tags:
    - [recurso]
    - aws
    - terraform
spec:
  owner: backstage  # ou user:guest para examples
  type: aws         # ou service para examples
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner  # apenas para Engie
      properties:
        name:
          title: Nome do [RECURSO]
          type: string
          description: Nome do recurso
          pattern: ^[a-zA-Z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição do recurso
        # Para Engie apenas:
        owner:
          title: GroupID
          type: string
          description: Responsável pelo recurso
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group

    - title: Configurações do [RECURSO]
      required:
        - parametro1
        - parametro2
      properties:
        parametro1:
          title: Parâmetro 1
          type: string
          description: Descrição do parâmetro
          default: valor-padrao
          enum:
            - opcao1
            - opcao2
          enumNames:
            - Opção 1
            - Opção 2
        parametro2:
          title: Parâmetro 2
          type: boolean
          description: Habilitar funcionalidade
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim

    - title: Configurações de Região
      required:
        - aws_region
      properties:
        aws_region:
          title: Região AWS
          type: string
          description: Região onde criar o recurso
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - sa-east-1

    # Para Examples (GitHub):
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com

    # Para Engie (Bitbucket):
    - title: Bitbucket
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repositório
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - almappro.tractebelenergia.com.br
            allowedOwners:
              - backstage
            allowedProjects:
              - BAC

  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton  # ou ./content para examples
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          # Mapear todos os parâmetros aqui
          parametro1: ${{ parameters.parametro1 }}
          parametro2: ${{ parameters.parametro2 }}
          aws_region: ${{ parameters.aws_region }}

    - id: publish
      name: Publish
      # Para Examples:
      action: publish:github
      # Para Engie:
      # action: publish:bitbucketServer
      input:
        allowedHosts:
          - github.com  # ou almappro.tractebelenergia.com.br
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}

    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml

  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
```

### **2. Arquivos Skeleton/Content**

#### **catalog-info.yaml**

```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: [recurso]-${{values.name}}
  description: [RECURSO] - ${{values.description}}
  annotations:
    github.com/project-slug: ${{values.destination.owner + "/" + values.destination.repo}}
    backstage.io/techdocs-ref: dir:.
spec:
  type: service
  lifecycle: production  # ou experimental para examples
  owner: ${{values.owner}}  # ou user:guest para examples
```

#### **Para Examples: main.tf**

```hcl
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  required_version = ">= 1.0"
}

provider "aws" {
  region = var.aws_region
}

# Seus recursos AWS aqui
resource "aws_[recurso]" "main" {
  # Configurações usando variáveis
  name = var.resource_name
  # outros parâmetros...

  tags = {
    Name        = var.resource_name
    Environment = "development"
    ManagedBy   = "terraform"
  }
}
```

#### **Para Engie: modules.tf**

```hcl
module "[recurso]" {
  source = "git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-[recurso].git"

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## [RECURSO] CONFIGURATION
  resource_name = var.resource_name
  parametro1    = var.parametro1
  parametro2    = var.parametro2

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
```

#### **variables.tf**

```hcl
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "${{ values.aws_region }}"
}

variable "resource_name" {
  description = "Name of the resource"
  type        = string
  default     = "${{ values.name }}"
}

variable "parametro1" {
  description = "Descrição do parâmetro 1"
  type        = string
  default     = "${{ values.parametro1 }}"
}

variable "parametro2" {
  description = "Descrição do parâmetro 2"
  type        = bool
  default     = ${{ values.parametro2 }}
}

# Para Engie:
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "production"
    ManagedBy   = "terraform"
  }
}
```

#### **outputs.tf**

```hcl
output "[recurso]_id" {
  description = "ID of the [recurso]"
  value       = aws_[recurso].main.id
}

output "[recurso]_arn" {
  description = "ARN of the [recurso]"
  value       = aws_[recurso].main.arn
}

# Outros outputs relevantes...
```

#### **provider.tf (apenas para Engie)**

```hcl
provider "aws" {
  region = var.aws_region
  default_tags {
    tags = var.resource_tags
  }
}
```

#### **versions.tf**

```hcl
terraform {
  required_version = ">= 1.3.1"  # ou ">= 1.0" para examples

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0.0"  # ou "~> 5.0" para examples
    }
  }
}
```

## 🔧 **Processo de Criação**

### **Passo 1: Escolher Padrão**

- **Examples**: Para recursos complexos, desenvolvimento, GitHub
- **Engie**: Para padronização corporativa, Bitbucket Server

### **Passo 2: Copiar Base**

```bash
# Examples
cp -r examples/ec2-instance examples/[novo-recurso]

# Engie
cp -r engie/template-terraform-ec2 engie/template-terraform-[novo-recurso]
```

### **Passo 3: Personalizar**

1. **template.yaml**: Metadados, parâmetros, steps
2. **main.tf/modules.tf**: Recursos Terraform
3. **variables.tf**: Variáveis templated
4. **outputs.tf**: Informações importantes
5. **catalog-info.yaml**: Metadados Backstage

### **Passo 4: Configurar**

```yaml
# app-config.yaml
- type: file
  target: ../../[examples|engie]/[template-name]/template.yaml
  rules:
    - allow: [Template]
```

### **Passo 5: Testar**

```bash
yarn dev
# Acesse Backstage → Create → Seu template
```

## 📚 **Recursos de Apoio**

### **Documentação**

- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws)
- [Backstage Templates](https://backstage.io/docs/features/software-templates/)
- [AWS Resource Documentation](https://docs.aws.amazon.com/)

### **Ferramentas**

- **terraform validate**: Validar sintaxe
- **terraform plan**: Visualizar mudanças
- **terraform fmt**: Formatar código

## 🎯 **Conclusão**

Com os padrões criados, você pode implementar **QUALQUER recurso AWS**:

✅ **API Gateway** ✅ **Lambda** ✅ **CloudFront** ✅ **Route 53**  
✅ **Kinesis** ✅ **Glue** ✅ **CodePipeline** ✅ **WAF**  
✅ **Transit Gateway** ✅ **Secrets Manager** ✅ **GuardDuty**  
✅ **E muito mais...**

O processo é sempre o mesmo: **copiar → personalizar → configurar → testar**! 🚀
