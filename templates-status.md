# 📋 Status dos Templates - Backstage

## ✅ **ATIVOS (Apenas LocalStack)**

### 🧪 **Templates LocalStack Locais (Sem GitHub)**
- ✅ `template-localstack-s3-local` - Bucket S3 offline
- ✅ `template-localstack-ec2-local` - Instância EC2 offline
- ✅ `template-localstack-dynamodb-local` - Tabela DynamoDB offline

**Características**:
- 🎯 **Geram arquivos** diretamente no disco
- 🚀 **Sem repositório** necessário
- ⚡ **Instantâneos** para usar
- 🧪 **Perfeitos para testes** LocalStack

## ❌ **DESATIVADOS TEMPORARIAMENTE**

### 📚 **Templates Examples**
- ❌ `examples/template` - Template base
- ❌ `examples/eks-cluster` - EKS Cluster
- ❌ `examples/ec2-instance` - EC2 Instance
- ❌ `examples/s3-bucket` - S3 Bucket
- ❌ `examples/security-group` - Security Group
- ❌ `examples/rds-database` - RDS Database
- ❌ `examples/vpc` - VPC

### 🏢 **Templates Engie**
- ❌ `engie/template-terraform-ec2` - EC2 Engie
- ❌ `engie/template-terraform-s3` - S3 Engie
- ❌ `engie/template-terraform-rds` - RDS Engie
- ❌ `engie/template-terraform-vpc` - VPC Engie
- ❌ `engie/template-terraform-eks` - EKS Engie

### 🔄 **Templates LocalStack GitHub**
- ❌ `local/template-localstack-s3-simple` - S3 simples
- ❌ `local/template-localstack-s3` - S3 completo
- ❌ `local/template-localstack-ec2` - EC2 GitHub
- ❌ `local/template-localstack-dynamodb` - DynamoDB GitHub

### 📦 **Outros Templates**
- ❌ `ec2-catalog/template` - EC2 Catalog

## 🎯 **Como Usar Agora**

### **1. Iniciar LocalStack**
```bash
docker run --rm -it -p 4566:4566 localstack/localstack
```

### **2. Iniciar Backstage**
```bash
export NODE_OPTIONS="--no-node-snapshot"
yarn dev
```

### **3. Acessar Templates**
1. **Acesse**: http://localhost:3000
2. **Create** → **Choose a template**
3. **Você verá apenas**:
   - `LocalStack S3 Local - Terraform`
   - `LocalStack EC2 Local - Terraform`
   - `LocalStack DynamoDB Local - Terraform`

### **4. Usar Template**
1. **Escolha** um template LocalStack
2. **Preencha** nome e configurações
3. **Crie** - arquivos gerados localmente
4. **Aplique**: `cd localstack-[recurso]-[nome] && terraform apply`

## 🔄 **Como Reativar Templates**

### **Para Reativar Examples**
```yaml
# app-config.yaml - Descomente as linhas:
- type: file
  target: ../../examples/template/template.yaml
  rules:
    - allow: [Template]
```

### **Para Reativar Engie**
```yaml
# app-config.yaml - Descomente as linhas:
- type: file
  target: ../../engie/template-terraform-ec2/template.yaml
  rules:
    - allow: [Template]
```

### **Para Reativar LocalStack GitHub**
```yaml
# app-config.yaml - Descomente as linhas:
- type: file
  target: ../../local/template-localstack-s3-simple/template.yaml
  rules:
    - allow: [Template]
```

## 📊 **Resumo Atual**

| Categoria | Total | Ativos | Desativados |
|-----------|-------|--------|-------------|
| **LocalStack Local** | 3 | ✅ 3 | ❌ 0 |
| **LocalStack GitHub** | 4 | ❌ 0 | ❌ 4 |
| **Examples** | 7 | ❌ 0 | ❌ 7 |
| **Engie** | 5 | ❌ 0 | ❌ 5 |
| **Outros** | 1 | ❌ 0 | ❌ 1 |
| **TOTAL** | 20 | **3** | **17** |

## 🎯 **Benefícios da Configuração Atual**

### ✅ **Interface Limpa**
- Apenas 3 templates relevantes
- Foco no LocalStack
- Sem confusão de opções

### ✅ **Testes Focados**
- Apenas templates offline
- Sem dependências externas
- Perfeito para desenvolvimento local

### ✅ **Facilidade de Uso**
- Escolha simples
- Processo direto
- Resultados imediatos

## 🔧 **Troubleshooting**

### **Templates não aparecem**
```bash
# Verificar configuração
grep -A 3 "localstack.*local" app-config.yaml

# Restart Backstage
yarn dev
```

### **Muitos templates aparecem**
- Verifique se as linhas estão comentadas com `#`
- Restart o Backstage após mudanças

### **Reativar todos os templates**
```bash
# Remover comentários de todas as linhas
sed -i 's/^    # - type: file/    - type: file/g' app-config.yaml
sed -i 's/^    #   target:/      target:/g' app-config.yaml
sed -i 's/^    #   rules:/      rules:/g' app-config.yaml
sed -i 's/^    #     - allow:/        - allow:/g' app-config.yaml
```

## 🎉 **Status Atual**

**✅ CONFIGURAÇÃO ATIVA**: Apenas templates LocalStack locais  
**🎯 FOCO**: Testes LocalStack offline  
**⚡ RESULTADO**: Interface limpa e focada  

Agora você tem apenas os 3 templates LocalStack locais ativos! 🚀

---

**Atualizado**: $(date)  
**Configuração**: Apenas LocalStack Local  
**Templates Ativos**: 3 de 20
