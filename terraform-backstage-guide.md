# 🚀 Guia Completo: Terraform + Backstage Templates

## 📋 Índice

- [Visão Geral](#visão-geral)
- [Estrutura do Padrão](#estrutura-do-padrão)
- [Componentes Essenciais](#componentes-essenciais)
- [Templates Disponíveis](#templates-disponíveis)
- [Como Criar Novos Templates](#como-criar-novos-templates)
- [Configuração e Deploy](#configuração-e-deploy)
- [Troubleshooting](#troubleshooting)
- [Melhores Práticas](#melhores-práticas)

## 🎯 Visão Geral

Este guia documenta a implementação de templates Terraform integrados ao Backstage para provisionamento de recursos AWS. O sistema permite que desenvolvedores criem infraestrutura de forma padronizada e self-service.

### Benefícios

- **🎯 Padronização**: Todos os recursos seguem a mesma estrutura
- **🔒 Segurança**: Configurações seguras por padrão
- **📝 Documentação**: Metadados automáticos no Backstage
- **🔄 Reutilização**: Templates facilmente adaptáveis
- **👥 Self-Service**: Desenvolvedores podem provisionar recursos
- **📊 Governança**: Controle centralizado de configurações

## 🏗️ Estrutura do Padrão

```
examples/
├── [recurso-aws]/
│   ├── template.yaml              # Definição do template Backstage
│   └── content/                   # Arquivos Terraform gerados
│       ├── catalog-info.yaml      # Metadados Backstage
│       ├── main.tf                # Recursos principais
│       ├── variables.tf           # Variáveis de entrada
│       ├── outputs.tf             # Outputs do Terraform
│       └── [arquivos específicos] # Scripts, configs, etc.
```

### Configuração do Catálogo

```yaml
# app-config.yaml
catalog:
  locations:
    - type: file
      target: ../../examples/[recurso]/template.yaml
      rules:
        - allow: [Template]
```

## 🔧 Componentes Essenciais

### 1. Template.yaml (Backstage)

```yaml
apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: recurso-terraform
  title: Título do Recurso
  description: Descrição do que o template faz
spec:
  owner: user:guest
  type: service
  parameters:
    # Formulários dinâmicos para o usuário
  steps:
    # Ações executadas (fetch, publish, register)
  output:
    # Links para repositório e catálogo
```

### 2. Terraform Files

#### main.tf

- Configuração do provider AWS
- Recursos principais
- Data sources necessários
- Configurações de segurança

#### variables.tf

- Parâmetros configuráveis vindos do Backstage
- Valores padrão
- Validações e descrições

#### outputs.tf

- Informações importantes após deploy
- IDs, ARNs, endpoints
- Dados para integração com outros recursos

#### catalog-info.yaml

- Metadados para o Backstage
- Tags e anotações
- Informações de ownership

## 📦 Templates Disponíveis

### 1. EKS Cluster

- **Localização**: `examples/eks-cluster/`
- **Descrição**: Cluster Kubernetes gerenciado
- **Recursos**: EKS cluster, node groups, VPC
- **Parâmetros**: Nome, região, tipo de instância, ação (apply/destroy)

### 2. EC2 Instance

- **Localização**: `examples/ec2-instance/`
- **Descrição**: Instância virtual com security group
- **Recursos**: EC2, Security Group, EIP opcional
- **Parâmetros**: Tipo de instância, região, key pair, IP público

### 3. S3 Bucket

- **Localização**: `examples/s3-bucket/`
- **Descrição**: Storage com configurações de segurança
- **Recursos**: S3 bucket, políticas, lifecycle, encryption
- **Parâmetros**: Propósito, versionamento, acesso público, lifecycle

### 4. Security Group

- **Localização**: `examples/security-group/`
- **Descrição**: Regras de firewall customizáveis
- **Recursos**: Security Group com regras predefinidas
- **Parâmetros**: Tipo (web, database, etc.), portas customizadas

### 5. RDS Database

- **Localização**: `examples/rds-database/`
- **Descrição**: Banco de dados gerenciado
- **Recursos**: RDS instance, subnet group, security group
- **Parâmetros**: Engine, classe, storage, backup, Multi-AZ

## 🛠️ Como Criar Novos Templates

### Passo 1: Criar Estrutura

```bash
mkdir -p examples/[novo-recurso]/content
```

### Passo 2: Template.yaml

```yaml
apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: [novo-recurso]-terraform
  title: [Título do Recurso]
  description: [Descrição]
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Basic Information
      required: [name, description]
      properties:
        name:
          title: Nome
          type: string
          ui:autofocus: true
        description:
          title: Descrição
          type: string
    - title: Repository Configuration
      required: [repoUrl]
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts: [github.com]
    - title: [Recurso] Configuration
      required: [region]
      properties:
        region:
          title: AWS Region
          type: string
          enum: [us-east-1, us-west-2, eu-west-1]
        # Adicionar parâmetros específicos
  steps:
    - id: fetch-base
      name: Fetch Base
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          # Mapear outros parâmetros
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
```

### Passo 3: Arquivos Terraform

#### content/main.tf

```hcl
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  required_version = ">= 1.0"
}

provider "aws" {
  region = var.aws_region
}

# Seus recursos AWS aqui
resource "aws_[recurso]" "main" {
  # Configurações usando variáveis
}
```

#### content/variables.tf

```hcl
variable "aws_region" {
  description = "AWS region"
  type        = string
  default     = "${{ values.region }}"
}

# Outras variáveis mapeadas dos parâmetros
```

#### content/outputs.tf

```hcl
output "[recurso]_id" {
  description = "ID do recurso"
  value       = aws_[recurso].main.id
}
```

#### content/catalog-info.yaml

```yaml
apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.name | dump }}
  description: ${{ values.description }}
  annotations:
    github.com/project-slug: ${{ values.destination.owner + "/" + values.destination.repo }}
  tags:
    - aws
    - [recurso]
    - terraform
spec:
  type: service
  owner: user:guest
  lifecycle: experimental
```

### Passo 4: Atualizar Configuração

```yaml
# app-config.yaml
catalog:
  locations:
    # Adicionar nova entrada
    - type: file
      target: ../../examples/[novo-recurso]/template.yaml
      rules:
        - allow: [Template]
```

### Passo 5: Restart Backstage

```bash
yarn dev
```

## 🚀 Configuração e Deploy

### Pré-requisitos

- Node.js 18+
- Yarn
- Conta AWS configurada
- GitHub token

### Configuração AWS

```bash
aws configure
# ou usar variáveis de ambiente
export AWS_ACCESS_KEY_ID=your_key
export AWS_SECRET_ACCESS_KEY=your_secret
export AWS_DEFAULT_REGION=us-east-1
```

### Configuração GitHub

```yaml
# app-config.yaml
integrations:
  github:
    - host: github.com
      token: ${GITHUB_TOKEN}
```

### Executar Backstage

```bash
yarn install
yarn dev
```

## 🔍 Troubleshooting

### Template não aparece na lista

1. Verificar configuração em `app-config.yaml`
2. Restart do Backstage
3. Refresh da location no Catalog → Locations

### Erro de sintaxe no template

1. Validar YAML syntax
2. Verificar indentação
3. Conferir mapeamento de variáveis

### Erro no Terraform

1. Verificar credenciais AWS
2. Validar sintaxe HCL
3. Conferir permissões IAM

### Logs úteis

```bash
# Backend logs
tail -f packages/backend/dist/bundle.log

# Browser console
F12 → Console
```

## 💡 Melhores Práticas

### Segurança

- Sempre usar encryption por padrão
- Implementar least privilege
- Usar secrets manager para senhas
- Habilitar logging e monitoring

### Terraform

- Usar remote state (S3 + DynamoDB)
- Implementar workspaces
- Versionamento de providers
- Outputs bem documentados

### Backstage

- Nomes descritivos para templates
- Validações nos parâmetros
- Tags consistentes
- Documentação clara

### Organização

- Estrutura de pastas consistente
- Naming conventions
- Versionamento de templates
- Testes automatizados

## 📚 Recursos Adicionais

- [Backstage Documentation](https://backstage.io/docs)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws)
- [AWS Best Practices](https://aws.amazon.com/architecture/well-architected/)
- [Terraform Best Practices](https://www.terraform.io/docs/cloud/guides/recommended-practices)

---

**Criado em**: $(date)
**Versão**: 1.0
**Autor**: Backstage + Terraform Integration Team
