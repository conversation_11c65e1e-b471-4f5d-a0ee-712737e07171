# Módulo DynamoDB Local - Simula módulo Engie para LocalStack
# Este módulo replica a estrutura do módulo Engie iac-terraform-aws-dynamodb

# DynamoDB Table
resource "aws_dynamodb_table" "main" {
  name           = var.table_name
  billing_mode   = var.billing_mode
  hash_key       = var.hash_key
  range_key      = var.range_key != "" ? var.range_key : null

  # Hash key attribute
  attribute {
    name = var.hash_key
    type = "S"  # String type
  }

  # Range key attribute (if specified)
  dynamic "attribute" {
    for_each = var.range_key != "" ? [1] : []
    content {
      name = var.range_key
      type = "S"  # String type
    }
  }

  # Point-in-time recovery
  point_in_time_recovery {
    enabled = var.enable_point_in_time_recovery
  }

  # Server-side encryption
  dynamic "server_side_encryption" {
    for_each = var.enable_server_side_encryption ? [1] : []
    content {
      enabled = true
    }
  }

  tags = merge(var.resource_tags, {
    Name = var.table_name
  })
}

# Sample items for testing (if enabled)
resource "aws_dynamodb_table_item" "sample_item_1" {
  count      = var.create_sample_data ? 1 : 0
  table_name = aws_dynamodb_table.main.name
  hash_key   = aws_dynamodb_table.main.hash_key
  range_key  = aws_dynamodb_table.main.range_key

  item = var.range_key != "" ? jsonencode({
    "${var.hash_key}" = {
      S = "engie-sample-1"
    }
    "${var.range_key}" = {
      S = "data-2024"
    }
    "name" = {
      S = "Primeiro Item Padrão Engie"
    }
    "description" = {
      S = "Item de exemplo seguindo padrão Engie"
    }
    "category" = {
      S = "corporativo"
    }
    "created_at" = {
      S = timestamp()
    }
    "active" = {
      BOOL = true
    }
    "version" = {
      N = "1"
    }
    "metadata" = {
      M = {
        "pattern" = {
          S = "engie"
        }
        "environment" = {
          S = "development"
        }
        "managed_by" = {
          S = "terraform"
        }
      }
    }
  }) : jsonencode({
    "${var.hash_key}" = {
      S = "engie-sample-1"
    }
    "name" = {
      S = "Primeiro Item Padrão Engie"
    }
    "description" = {
      S = "Item de exemplo seguindo padrão Engie"
    }
    "category" = {
      S = "corporativo"
    }
    "created_at" = {
      S = timestamp()
    }
    "active" = {
      BOOL = true
    }
    "version" = {
      N = "1"
    }
    "metadata" = {
      M = {
        "pattern" = {
          S = "engie"
        }
        "environment" = {
          S = "development"
        }
        "managed_by" = {
          S = "terraform"
        }
      }
    }
  })
}

resource "aws_dynamodb_table_item" "sample_item_2" {
  count      = var.create_sample_data ? 1 : 0
  table_name = aws_dynamodb_table.main.name
  hash_key   = aws_dynamodb_table.main.hash_key
  range_key  = aws_dynamodb_table.main.range_key

  item = var.range_key != "" ? jsonencode({
    "${var.hash_key}" = {
      S = "engie-sample-2"
    }
    "${var.range_key}" = {
      S = "config-2024"
    }
    "name" = {
      S = "Segundo Item Padrão Engie"
    }
    "description" = {
      S = "Configuração corporativa de exemplo"
    }
    "category" = {
      S = "configuracao"
    }
    "created_at" = {
      S = timestamp()
    }
    "active" = {
      BOOL = true
    }
    "version" = {
      N = "2"
    }
    "tags" = {
      SS = ["engie", "corporativo", "localstack"]
    }
  }) : jsonencode({
    "${var.hash_key}" = {
      S = "engie-sample-2"
    }
    "name" = {
      S = "Segundo Item Padrão Engie"
    }
    "description" = {
      S = "Configuração corporativa de exemplo"
    }
    "category" = {
      S = "configuracao"
    }
    "created_at" = {
      S = timestamp()
    }
    "active" = {
      BOOL = true
    }
    "version" = {
      N = "2"
    }
    "tags" = {
      SS = ["engie", "corporativo", "localstack"]
    }
  })
}
