#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default     = "${{ values.aws_region }}"
  type        = string
}

#TABLE CONFIGURATION
variable "table_name" {
  description = "Name of the DynamoDB table"
  default     = "${{ values.name }}"
  type        = string
}

variable "hash_key" {
  description = "Hash key (partition key) for the table"
  default     = "${{ values.hash_key }}"
  type        = string
}

variable "range_key" {
  description = "Range key (sort key) for the table"
  default     = "${{ values.range_key }}"
  type        = string
}

variable "billing_mode" {
  description = "Billing mode for the table"
  default     = "${{ values.billing_mode }}"
  type        = string
}

#FEATURES CONFIGURATION
variable "enable_point_in_time_recovery" {
  description = "Enable point-in-time recovery"
  default     = true
  type        = bool
}

variable "enable_server_side_encryption" {
  description = "Enable server-side encryption"
  default     = true
  type        = bool
}

variable "create_sample_data" {
  description = "Create sample data in the table"
  default     = ${{ values.create_sample_data }}
  type        = bool
}

#LOCALSTACK CONFIGURATION
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL"
  default     = "${{ values.localstack_endpoint }}"
  type        = string
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Service     = "dynamodb"
    Owner       = "${{ values.owner }}"
  }
}
