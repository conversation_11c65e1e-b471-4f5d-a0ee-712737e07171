apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: dynamodb-${{values.name}}
  description: DynamoDB Table - ${{values.description}}
  annotations:
    github.com/project-slug: localstack-dynamodb-${{values.name}}
    backstage.io/techdocs-ref: dir:.
  tags:
    - dynamodb
    - localstack
    - development
    - engie
    - database
spec:
  type: service
  lifecycle: production
  owner: ${{values.owner}}
