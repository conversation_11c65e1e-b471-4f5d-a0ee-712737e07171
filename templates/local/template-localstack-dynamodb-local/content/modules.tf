# Módulo DynamoDB seguindo padrão Engie
# Em produção: git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-dynamodb.git
# Para LocalStack: módulo local simulando o comportamento

module "dynamodb" {
  source = "./modules/dynamodb"  # Módulo local para LocalStack

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## TABLE CONFIGURATION
  table_name   = var.table_name
  hash_key     = var.hash_key
  range_key    = var.range_key
  billing_mode = var.billing_mode
  
  ## FEATURES CONFIGURATION
  enable_point_in_time_recovery = var.enable_point_in_time_recovery
  enable_server_side_encryption = var.enable_server_side_encryption
  
  ## SAMPLE DATA
  create_sample_data = var.create_sample_data

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
