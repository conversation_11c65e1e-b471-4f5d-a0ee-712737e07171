# LocalStack DynamoDB - ${{ values.name }}

Tabela DynamoDB criada para desenvolvimento local com LocalStack.

## 📋 Informações

- **Nome**: ${{ values.name }}
- **Descrição**: ${{ values.description }}
- **Hash Key**: ${{ values.hash_key }}
{% if values.range_key %}- **Range Key**: ${{ values.range_key }}{% endif %}
- **Billing Mode**: ${{ values.billing_mode }}
- **Endpoint**: ${{ values.localstack_endpoint }}
- **Região**: ${{ values.aws_region }}
- **Dados de Exemplo**: ${{ values.create_sample_data }}

## 🚀 Como Usar

### 1. Iniciar LocalStack
```bash
docker run --rm -it -p 4566:4566 localstack/localstack
```

### 2. Aplicar Terraform
```bash
terraform init
terraform plan
terraform apply
```

### 3. Testar Tabela
```bash
# Listar tabelas
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb list-tables

# Descrever tabela
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb describe-table --table-name ${{ values.name }}

# Scan todos os itens
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb scan --table-name ${{ values.name }}
```

## 📊 Operações CRUD

### Create (Inserir Item)
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb put-item \
  --table-name ${{ values.name }} \
  --item '{
    "${{ values.hash_key }}": {"S": "meu-id"},
    {% if values.range_key %}"${{ values.range_key }}": {"S": "meu-sort"},{% endif %}
    "name": {"S": "Meu Item"},
    "description": {"S": "Descrição do meu item"},
    "active": {"BOOL": true},
    "count": {"N": "1"}
  }'
```

### Read (Buscar Item)
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb get-item \
  --table-name ${{ values.name }} \
  --key '{
    "${{ values.hash_key }}": {"S": "meu-id"}{% if values.range_key %},
    "${{ values.range_key }}": {"S": "meu-sort"}{% endif %}
  }'
```

{% if values.range_key %}
### Query (Buscar por Partition Key)
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb query \
  --table-name ${{ values.name }} \
  --key-condition-expression "${{ values.hash_key }} = :pk" \
  --expression-attribute-values '{
    ":pk": {"S": "sample-id-1"}
  }'
```
{% endif %}

### Update (Atualizar Item)
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb update-item \
  --table-name ${{ values.name }} \
  --key '{
    "${{ values.hash_key }}": {"S": "meu-id"}{% if values.range_key %},
    "${{ values.range_key }}": {"S": "meu-sort"}{% endif %}
  }' \
  --update-expression "SET #n = :val, #c = #c + :inc" \
  --expression-attribute-names '{
    "#n": "name",
    "#c": "count"
  }' \
  --expression-attribute-values '{
    ":val": {"S": "Nome Atualizado"},
    ":inc": {"N": "1"}
  }'
```

### Delete (Deletar Item)
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb delete-item \
  --table-name ${{ values.name }} \
  --key '{
    "${{ values.hash_key }}": {"S": "meu-id"}{% if values.range_key %},
    "${{ values.range_key }}": {"S": "meu-sort"}{% endif %}
  }'
```

## 🔍 Operações Avançadas

### Scan com Filtro
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb scan \
  --table-name ${{ values.name }} \
  --filter-expression "active = :active" \
  --expression-attribute-values '{
    ":active": {"BOOL": true}
  }'
```

### Batch Write (Múltiplos Itens)
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb batch-write-item \
  --request-items '{
    "${{ values.name }}": [
      {
        "PutRequest": {
          "Item": {
            "${{ values.hash_key }}": {"S": "batch-1"},
            {% if values.range_key %}"${{ values.range_key }}": {"S": "sort-1"},{% endif %}
            "name": {"S": "Item Batch 1"}
          }
        }
      },
      {
        "PutRequest": {
          "Item": {
            "${{ values.hash_key }}": {"S": "batch-2"},
            {% if values.range_key %}"${{ values.range_key }}": {"S": "sort-2"},{% endif %}
            "name": {"S": "Item Batch 2"}
          }
        }
      }
    ]
  }'
```

### Batch Get (Múltiplas Leituras)
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb batch-get-item \
  --request-items '{
    "${{ values.name }}": {
      "Keys": [
        {
          "${{ values.hash_key }}": {"S": "sample-id-1"}{% if values.range_key %},
          "${{ values.range_key }}": {"S": "sample-sort-1"}{% endif %}
        },
        {
          "${{ values.hash_key }}": {"S": "sample-id-2"}{% if values.range_key %},
          "${{ values.range_key }}": {"S": "sample-sort-2"}{% endif %}
        }
      ]
    }
  }'
```

{% if values.create_sample_data %}
## 📁 Dados de Exemplo

A tabela foi criada com os seguintes itens de exemplo:

1. **sample-id-1**: Primeiro item de exemplo
2. **sample-id-2**: Segundo item de exemplo  
3. **sample-id-3**: Terceiro item com dados complexos

Cada item contém:
- `name`: Nome descritivo
- `description`: Descrição do item
- `category`: Categoria do item
- `created_at`: Timestamp de criação
- `active`: Status ativo/inativo
- `count`: Contador numérico
- `metadata`: Dados aninhados (item 3)
- `tags`: Lista de tags (item 3)
{% endif %}

## 🔧 Troubleshooting

### LocalStack não responde
```bash
# Verificar se está rodando
curl ${{ values.localstack_endpoint }}/health

# Ver logs
docker logs $(docker ps -q --filter ancestor=localstack/localstack)
```

### Terraform falha
```bash
# Validar configuração
terraform validate

# Debug
export TF_LOG=DEBUG
terraform apply
```

### Tabela não aparece
```bash
# Verificar endpoint
aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb list-tables

# Verificar região
aws --endpoint-url=${{ values.localstack_endpoint }} --region ${{ values.aws_region }} dynamodb list-tables
```

## 🎯 Próximos Passos

1. Teste as operações CRUD básicas
2. Experimente queries e scans
3. Teste operações em lote
4. Explore índices secundários (se necessário)
5. Adapte conforme suas necessidades

---

**Criado com**: Backstage + LocalStack  
**Ambiente**: Desenvolvimento Local
