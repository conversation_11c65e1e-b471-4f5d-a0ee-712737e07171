# Provider AWS configurado para LocalStack (desenvolvimento)
# Em produção, usar configuração padrão Engie
provider "aws" {
  access_key = "sample"
  secret_key = "sample"
  region     = var.aws_region

  # Configurações específicas LocalStack
  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    dynamodb = var.localstack_endpoint
  }

  default_tags {
    tags = var.resource_tags
  }
}
