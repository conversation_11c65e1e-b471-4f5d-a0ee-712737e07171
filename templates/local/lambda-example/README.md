# ⚡ Lambda Example - LocalStack

Este exemplo demonstra como criar e testar funções Lambda com API Gateway usando Terraform e LocalStack.

## 📦 O que será criado

- ✅ **Lambda Function** (Python 3.9)
- ✅ **IAM Role** e políticas
- ✅ **CloudWatch Log Group**
- ✅ **API Gateway** REST API
- ✅ **API Gateway Integration** com Lambda
- ✅ **Deployment** automático

## 🚀 Como executar

### **1. Iniciar LocalStack**
```bash
# Via Docker (recomendado)
docker run --rm -it \
  -p 4566:4566 \
  -e DEBUG=1 \
  localstack/localstack
```

### **2. Executar Terraform**
```bash
# Inicializar
terraform init

# Planejar
terraform plan

# Aplicar
terraform apply
```

### **3. Testar a função Lambda**
```bash
# Invocar diretamente
aws --endpoint-url=http://localhost:4566 lambda invoke \
  --function-name localstack-test-function \
  --payload '{"name": "LocalStack", "action": "test"}' \
  response.json

# Ver resposta
cat response.json | jq .
```

### **4. Testar via API Gateway**
```bash
# Fazer requisição HTTP
curl -X POST http://localhost:4566/restapis/[API-ID]/dev/_user_request_/test \
  -H 'Content-Type: application/json' \
  -d '{"user": "tester", "message": "Hello API!"}'
```

## 🔍 Comandos úteis

### **Gerenciar Lambda**
```bash
# Listar funções
aws --endpoint-url=http://localhost:4566 lambda list-functions

# Ver detalhes da função
aws --endpoint-url=http://localhost:4566 lambda get-function \
  --function-name localstack-test-function

# Ver configuração
aws --endpoint-url=http://localhost:4566 lambda get-function-configuration \
  --function-name localstack-test-function

# Atualizar variáveis de ambiente
aws --endpoint-url=http://localhost:4566 lambda update-function-configuration \
  --function-name localstack-test-function \
  --environment Variables='{CUSTOM_MESSAGE=Updated message!,DEBUG_MODE=true}'
```

### **Monitorar Logs**
```bash
# Listar log streams
aws --endpoint-url=http://localhost:4566 logs describe-log-streams \
  --log-group-name /aws/lambda/localstack-test-function

# Ver logs (se disponível)
aws --endpoint-url=http://localhost:4566 logs get-log-events \
  --log-group-name /aws/lambda/localstack-test-function \
  --log-stream-name [LOG-STREAM-NAME]
```

### **Testar API Gateway**
```bash
# Listar APIs
aws --endpoint-url=http://localhost:4566 apigateway get-rest-apis

# Ver recursos da API
aws --endpoint-url=http://localhost:4566 apigateway get-resources \
  --rest-api-id [API-ID]

# Testar endpoint
curl -v -X POST http://localhost:4566/restapis/[API-ID]/dev/_user_request_/test \
  -H 'Content-Type: application/json' \
  -d '{"test": "data", "timestamp": "'$(date -u +%Y-%m-%dT%H:%M:%SZ)'"}'
```

## 🧪 Testes avançados

### **Teste de performance**
```bash
# Múltiplas invocações
for i in {1..5}; do
  aws --endpoint-url=http://localhost:4566 lambda invoke \
    --function-name localstack-test-function \
    --payload "{\"test_number\": $i, \"timestamp\": \"$(date -u +%Y-%m-%dT%H:%M:%SZ)\"}" \
    response_$i.json
  echo "Test $i completed"
done
```

### **Teste com diferentes payloads**
```bash
# Payload simples
echo '{"message": "simple test"}' | \
aws --endpoint-url=http://localhost:4566 lambda invoke \
  --function-name localstack-test-function \
  --cli-binary-format raw-in-base64-out \
  --payload file:///dev/stdin \
  simple_response.json

# Payload complexo
echo '{
  "user": {
    "id": 123,
    "name": "Test User",
    "email": "<EMAIL>"
  },
  "action": "process_data",
  "data": [1, 2, 3, 4, 5],
  "metadata": {
    "source": "localstack",
    "version": "1.0"
  }
}' | \
aws --endpoint-url=http://localhost:4566 lambda invoke \
  --function-name localstack-test-function \
  --cli-binary-format raw-in-base64-out \
  --payload file:///dev/stdin \
  complex_response.json
```

### **Teste via API Gateway com curl**
```bash
# POST com dados JSON
curl -s -X POST http://localhost:4566/restapis/[API-ID]/dev/_user_request_/test \
  -H 'Content-Type: application/json' \
  -H 'X-Custom-Header: test-value' \
  -d '{
    "action": "api_test",
    "user": "api_tester",
    "data": {
      "items": ["item1", "item2", "item3"],
      "count": 3
    }
  }' | jq .

# GET (se configurado)
curl -s http://localhost:4566/restapis/[API-ID]/dev/_user_request_/test?param1=value1&param2=value2
```

## 📁 Arquivos criados

```
lambda-example/
├── main.tf                    # Configuração Terraform
├── outputs.tf                 # Outputs e comandos úteis
├── lambda_function.py         # Código da função Lambda
├── lambda_function.zip        # Package de deployment
└── README.md                  # Esta documentação
```

## 🔧 Personalização

### **Modificar o código Lambda**
1. Edite o arquivo `lambda_function.py` gerado
2. Execute `terraform apply` para atualizar

### **Adicionar dependências Python**
```bash
# Criar requirements.txt
echo "requests==2.28.1" > requirements.txt

# Instalar em diretório local
pip install -r requirements.txt -t ./python-packages/

# Atualizar o zip no Terraform para incluir as dependências
```

### **Configurar diferentes triggers**
- **S3 Events**: Adicionar trigger para uploads
- **CloudWatch Events**: Execução agendada
- **SQS**: Processamento de filas
- **DynamoDB Streams**: Mudanças em tabelas

## 🧹 Limpeza

```bash
# Destruir recursos
terraform destroy

# Limpar arquivos gerados
rm -f lambda_function.py lambda_function.zip response*.json
```

## 📚 Recursos relacionados

- **Lambda Developer Guide**: https://docs.aws.amazon.com/lambda/
- **LocalStack Lambda**: https://docs.localstack.cloud/user-guide/aws/lambda/
- **API Gateway**: https://docs.aws.amazon.com/apigateway/
- **Terraform AWS Lambda**: https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/lambda_function

## 🎯 Próximos passos

1. **Adicionar** mais triggers (S3, SQS, CloudWatch)
2. **Implementar** error handling
3. **Configurar** diferentes ambientes
4. **Integrar** com outros serviços LocalStack
