# DynamoDB Table
resource "aws_dynamodb_table" "main" {
  name           = var.table_name
  billing_mode   = var.billing_mode
  hash_key       = var.hash_key
  range_key      = var.range_key != "" ? var.range_key : null

  # Provisioned throughput (only if billing_mode is PROVISIONED)
  read_capacity  = var.billing_mode == "PROVISIONED" ? var.read_capacity : null
  write_capacity = var.billing_mode == "PROVISIONED" ? var.write_capacity : null

  # Hash key attribute
  attribute {
    name = var.hash_key
    type = "S"  # String type
  }

  # Range key attribute (if specified)
  dynamic "attribute" {
    for_each = var.range_key != "" ? [1] : []
    content {
      name = var.range_key
      type = "S"  # String type
    }
  }

  # Point-in-time recovery
  point_in_time_recovery {
    enabled = var.enable_point_in_time_recovery
  }

  # Server-side encryption
  dynamic "server_side_encryption" {
    for_each = var.enable_server_side_encryption ? [1] : []
    content {
      enabled = true
    }
  }

  tags = merge(var.resource_tags, {
    Name = var.table_name
  })
}

# Sample items for testing
resource "aws_dynamodb_table_item" "sample_item_1" {
  table_name = aws_dynamodb_table.main.name
  hash_key   = aws_dynamodb_table.main.hash_key
  range_key  = aws_dynamodb_table.main.range_key

  item = var.range_key != "" ? jsonencode({
    "${var.hash_key}" = {
      S = "sample-id-1"
    }
    "${var.range_key}" = {
      S = "sample-sort-1"
    }
    "data" = {
      S = "Sample data from LocalStack DynamoDB"
    }
    "timestamp" = {
      S = timestamp()
    }
  }) : jsonencode({
    "${var.hash_key}" = {
      S = "sample-id-1"
    }
    "data" = {
      S = "Sample data from LocalStack DynamoDB"
    }
    "timestamp" = {
      S = timestamp()
    }
  })
}

resource "aws_dynamodb_table_item" "sample_item_2" {
  table_name = aws_dynamodb_table.main.name
  hash_key   = aws_dynamodb_table.main.hash_key
  range_key  = aws_dynamodb_table.main.range_key

  item = var.range_key != "" ? jsonencode({
    "${var.hash_key}" = {
      S = "sample-id-2"
    }
    "${var.range_key}" = {
      S = "sample-sort-2"
    }
    "data" = {
      S = "Another sample item for testing"
    }
    "timestamp" = {
      S = timestamp()
    }
  }) : jsonencode({
    "${var.hash_key}" = {
      S = "sample-id-2"
    }
    "data" = {
      S = "Another sample item for testing"
    }
    "timestamp" = {
      S = timestamp()
    }
  })
}
