output "table_name" {
  description = "Name of the DynamoDB table"
  value       = aws_dynamodb_table.main.name
}

output "table_arn" {
  description = "ARN of the DynamoDB table"
  value       = aws_dynamodb_table.main.arn
}

output "table_id" {
  description = "ID of the DynamoDB table"
  value       = aws_dynamodb_table.main.id
}

output "hash_key" {
  description = "Hash key of the table"
  value       = aws_dynamodb_table.main.hash_key
}

output "range_key" {
  description = "Range key of the table"
  value       = aws_dynamodb_table.main.range_key
}

output "billing_mode" {
  description = "Billing mode of the table"
  value       = aws_dynamodb_table.main.billing_mode
}

output "localstack_endpoint" {
  description = "LocalStack endpoint used"
  value       = var.localstack_endpoint
}

output "aws_cli_commands" {
  description = "Useful AWS CLI commands for testing"
  value = {
    list_tables = "aws dynamodb list-tables --endpoint-url=${var.localstack_endpoint} --region=${var.localstack_region}"
    describe_table = "aws dynamodb describe-table --table-name=${aws_dynamodb_table.main.name} --endpoint-url=${var.localstack_endpoint} --region=${var.localstack_region}"
    scan_table = "aws dynamodb scan --table-name=${aws_dynamodb_table.main.name} --endpoint-url=${var.localstack_endpoint} --region=${var.localstack_region}"
  }
}
