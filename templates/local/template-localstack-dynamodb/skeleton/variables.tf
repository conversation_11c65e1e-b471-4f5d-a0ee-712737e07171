#LOCALSTACK CONFIGURATION
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL"
  default     = "${{ values.localstack_endpoint }}"
  type        = string
}

variable "localstack_region" {
  description = "LocalStack simulated region"
  default     = "${{ values.localstack_region }}"
  type        = string
}

#TABLE CONFIGURATION
variable "table_name" {
  description = "Name of the DynamoDB table"
  default     = "${{ values.name }}"
  type        = string
}

variable "hash_key" {
  description = "Hash key (partition key) for the table"
  default     = "${{ values.hash_key }}"
  type        = string
}

variable "range_key" {
  description = "Range key (sort key) for the table"
  default     = "${{ values.range_key }}"
  type        = string
}

variable "billing_mode" {
  description = "Billing mode for the table"
  default     = "${{ values.billing_mode }}"
  type        = string
}

{% if values.billing_mode == "PROVISIONED" %}
variable "read_capacity" {
  description = "Read capacity units for the table"
  default     = ${{ values.read_capacity }}
  type        = number
}

variable "write_capacity" {
  description = "Write capacity units for the table"
  default     = ${{ values.write_capacity }}
  type        = number
}
{% endif %}

#FEATURES CONFIGURATION
variable "enable_point_in_time_recovery" {
  description = "Enable point-in-time recovery"
  default     = ${{ values.enable_point_in_time_recovery }}
  type        = bool
}

variable "enable_server_side_encryption" {
  description = "Enable server-side encryption"
  default     = ${{ values.enable_server_side_encryption }}
  type        = bool
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Service     = "dynamodb"
  }
}
