apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: localstack-dynamodb-${{values.name}}
  description: LocalStack DynamoDB Table - ${{values.description}}
  annotations:
    github.com/project-slug: ${{values.destination.owner + "/" + values.destination.repo}}
    backstage.io/techdocs-ref: dir:.
  tags:
    - localstack
    - dynamodb
    - database
    - development
    - local
spec:
  type: service
  lifecycle: experimental
  owner: ${{values.owner}}
