output "users_table_name" {
  description = "Name of the Users DynamoDB table"
  value       = aws_dynamodb_table.users_table.name
}

output "users_table_arn" {
  description = "ARN of the Users DynamoDB table"
  value       = aws_dynamodb_table.users_table.arn
}

output "products_table_name" {
  description = "Name of the Products DynamoDB table"
  value       = aws_dynamodb_table.products_table.name
}

output "products_table_arn" {
  description = "ARN of the Products DynamoDB table"
  value       = aws_dynamodb_table.products_table.arn
}

output "orders_table_name" {
  description = "Name of the Orders DynamoDB table"
  value       = aws_dynamodb_table.orders_table.name
}

output "orders_table_arn" {
  description = "ARN of the Orders DynamoDB table"
  value       = aws_dynamodb_table.orders_table.arn
}

output "localstack_commands" {
  description = "Useful LocalStack commands for testing DynamoDB"
  value = {
    list_tables = "aws --endpoint-url=http://localhost:4566 dynamodb list-tables"
    describe_users_table = "aws --endpoint-url=http://localhost:4566 dynamodb describe-table --table-name ${aws_dynamodb_table.users_table.name}"
    scan_users = "aws --endpoint-url=http://localhost:4566 dynamodb scan --table-name ${aws_dynamodb_table.users_table.name}"
    scan_products = "aws --endpoint-url=http://localhost:4566 dynamodb scan --table-name ${aws_dynamodb_table.products_table.name}"
    scan_orders = "aws --endpoint-url=http://localhost:4566 dynamodb scan --table-name ${aws_dynamodb_table.orders_table.name}"
  }
}

output "query_examples" {
  description = "Example queries for testing"
  value = {
    get_user = "aws --endpoint-url=http://localhost:4566 dynamodb get-item --table-name ${aws_dynamodb_table.users_table.name} --key '{\"user_id\":{\"S\":\"user-1\"},\"created_at\":{\"S\":\"2024-01-01T10:00:00Z\"}}'"
    query_user_by_email = "aws --endpoint-url=http://localhost:4566 dynamodb query --table-name ${aws_dynamodb_table.users_table.name} --index-name email-index --key-condition-expression 'email = :email' --expression-attribute-values '{\":email\":{\"S\":\"<EMAIL>\"}}'"
    query_products_by_category = "aws --endpoint-url=http://localhost:4566 dynamodb query --table-name ${aws_dynamodb_table.products_table.name} --index-name category-price-index --key-condition-expression 'category = :cat' --expression-attribute-values '{\":cat\":{\"S\":\"electronics\"}}'"
    query_user_orders = "aws --endpoint-url=http://localhost:4566 dynamodb query --table-name ${aws_dynamodb_table.orders_table.name} --index-name user-orders-index --key-condition-expression 'user_id = :uid' --expression-attribute-values '{\":uid\":{\"S\":\"user-1\"}}'"
  }
}

output "crud_examples" {
  description = "CRUD operation examples"
  value = {
    create_user = "aws --endpoint-url=http://localhost:4566 dynamodb put-item --table-name ${aws_dynamodb_table.users_table.name} --item '{\"user_id\":{\"S\":\"user-new\"},\"created_at\":{\"S\":\"2024-01-15T10:00:00Z\"},\"email\":{\"S\":\"<EMAIL>\"},\"name\":{\"S\":\"New User\"},\"status\":{\"S\":\"active\"}}'"
    update_user = "aws --endpoint-url=http://localhost:4566 dynamodb update-item --table-name ${aws_dynamodb_table.users_table.name} --key '{\"user_id\":{\"S\":\"user-1\"},\"created_at\":{\"S\":\"2024-01-01T10:00:00Z\"}}' --update-expression 'SET #status = :status' --expression-attribute-names '{\"#status\":\"status\"}' --expression-attribute-values '{\":status\":{\"S\":\"updated\"}}'"
    delete_user = "aws --endpoint-url=http://localhost:4566 dynamodb delete-item --table-name ${aws_dynamodb_table.users_table.name} --key '{\"user_id\":{\"S\":\"user-new\"},\"created_at\":{\"S\":\"2024-01-15T10:00:00Z\"}}'"
  }
}

output "batch_operations" {
  description = "Batch operation examples"
  value = {
    batch_get = "aws --endpoint-url=http://localhost:4566 dynamodb batch-get-item --request-items '{\"${aws_dynamodb_table.users_table.name}\":{\"Keys\":[{\"user_id\":{\"S\":\"user-1\"},\"created_at\":{\"S\":\"2024-01-01T10:00:00Z\"}},{\"user_id\":{\"S\":\"user-2\"},\"created_at\":{\"S\":\"2024-01-02T10:00:00Z\"}}]}}'"
    batch_write = "aws --endpoint-url=http://localhost:4566 dynamodb batch-write-item --request-items '{\"${aws_dynamodb_table.products_table.name}\":[{\"PutRequest\":{\"Item\":{\"product_id\":{\"S\":\"batch-1\"},\"name\":{\"S\":\"Batch Product\"},\"category\":{\"S\":\"test\"},\"price\":{\"N\":\"99.99\"}}}}]}'"
  }
}

output "table_info" {
  description = "Information about created tables"
  value = {
    users_table = {
      name = aws_dynamodb_table.users_table.name
      billing_mode = aws_dynamodb_table.users_table.billing_mode
      hash_key = aws_dynamodb_table.users_table.hash_key
      range_key = aws_dynamodb_table.users_table.range_key
      global_secondary_indexes = [
        "email-index (hash: email)",
        "status-index (hash: status, range: created_at)"
      ]
    }
    products_table = {
      name = aws_dynamodb_table.products_table.name
      billing_mode = aws_dynamodb_table.products_table.billing_mode
      hash_key = aws_dynamodb_table.products_table.hash_key
      read_capacity = aws_dynamodb_table.products_table.read_capacity
      write_capacity = aws_dynamodb_table.products_table.write_capacity
      global_secondary_indexes = [
        "category-price-index (hash: category, range: price)"
      ]
    }
    orders_table = {
      name = aws_dynamodb_table.orders_table.name
      billing_mode = aws_dynamodb_table.orders_table.billing_mode
      hash_key = aws_dynamodb_table.orders_table.hash_key
      ttl_enabled = true
      ttl_attribute = "expires_at"
      global_secondary_indexes = [
        "user-orders-index (hash: user_id, range: order_date)"
      ]
    }
  }
}
