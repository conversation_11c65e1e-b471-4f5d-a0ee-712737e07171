# Módulo EC2 seguindo padrão Engie
# Em produção: git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-ec2.git
# Para LocalStack: módulo local simulando o comportamento

module "ec2" {
  source = "./modules/ec2"  # Módulo local para LocalStack

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## INSTANCE CONFIGURATION
  instance_name = var.instance_name
  instance_type = var.instance_type
  ami_id        = var.ami_id
  
  ## NETWORK CONFIGURATION
  enable_public_ip = var.enable_public_ip
  
  ## SECURITY CONFIGURATION
  key_name   = var.key_name
  public_key = var.public_key
  
  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
