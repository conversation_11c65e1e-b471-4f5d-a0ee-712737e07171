#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default     = "${{ values.aws_region }}"
  type        = string
}

#INSTANCE CONFIGURATION
variable "instance_name" {
  description = "Name of the EC2 instance"
  default     = "${{ values.name }}"
  type        = string
}

variable "instance_type" {
  description = "EC2 instance type"
  default     = "${{ values.instance_type }}"
  type        = string
}

variable "ami_id" {
  description = "AMI ID for the instance"
  default     = "${{ values.ami_id }}"
  type        = string
}

#NETWORK CONFIGURATION
variable "enable_public_ip" {
  description = "Associate public IP address"
  default     = ${{ values.enable_public_ip }}
  type        = bool
}

#SECURITY CONFIGURATION
variable "key_name" {
  description = "Name of the AWS key pair"
  default     = "${{ values.name }}-key"
  type        = string
}

variable "public_key" {
  description = "Public key for SSH access"
  default     = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDZ6+sample+key+for+localstack+testing"
  type        = string
}

#LOCALSTACK CONFIGURATION
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL"
  default     = "${{ values.localstack_endpoint }}"
  type        = string
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Owner       = "${{ values.owner }}"
  }
}
