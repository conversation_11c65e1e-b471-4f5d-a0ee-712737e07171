variable "instance_name" {
  description = "Name of the EC2 instance"
  type        = string
}

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
}

variable "ami_id" {
  description = "AMI ID for the instance"
  type        = string
}

variable "enable_public_ip" {
  description = "Associate public IP address"
  type        = bool
  default     = true
}

variable "key_name" {
  description = "Name of the AWS key pair"
  type        = string
}

variable "public_key" {
  description = "Public key for SSH access"
  type        = string
}

variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default     = {}
}
