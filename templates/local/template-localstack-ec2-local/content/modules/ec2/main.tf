# Módulo EC2 Local - Simula módulo Engie para LocalStack
# Este módulo replica a estrutura do módulo Engie iac-terraform-aws-ec2

# Default VPC (LocalStack creates one automatically)
data "aws_vpc" "default" {
  default = true
}

# Default subnet
data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

# Key Pair
resource "aws_key_pair" "main" {
  key_name   = var.key_name
  public_key = var.public_key

  tags = merge(var.resource_tags, {
    Name = var.key_name
  })
}

# Security Group
resource "aws_security_group" "main" {
  name_prefix = "${var.instance_name}-sg"
  description = "Security group for ${var.instance_name} LocalStack instance"
  vpc_id      = data.aws_vpc.default.id

  # SSH access
  ingress {
    description = "SSH"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTP access
  ingress {
    description = "HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS access
  ingress {
    description = "HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom port for testing
  ingress {
    description = "Custom App"
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # All outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = merge(var.resource_tags, {
    Name = "${var.instance_name}-sg"
  })
}

# EC2 Instance
resource "aws_instance" "main" {
  ami                    = var.ami_id
  instance_type          = var.instance_type
  key_name              = aws_key_pair.main.key_name
  vpc_security_group_ids = [aws_security_group.main.id]
  subnet_id             = data.aws_subnets.default.ids[0]
  
  associate_public_ip_address = var.enable_public_ip

  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    instance_name = var.instance_name
  }))

  tags = merge(var.resource_tags, {
    Name = var.instance_name
  })
}
