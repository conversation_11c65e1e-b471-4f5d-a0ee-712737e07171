output "instance_id" {
  description = "ID da instância EC2"
  value       = module.ec2.instance_id
}

output "instance_public_ip" {
  description = "IP público da instância"
  value       = module.ec2.instance_public_ip
}

output "instance_private_ip" {
  description = "IP privado da instância"
  value       = module.ec2.instance_private_ip
}

output "instance_public_dns" {
  description = "DNS público da instância"
  value       = module.ec2.instance_public_dns
}

output "security_group_id" {
  description = "ID do security group"
  value       = module.ec2.security_group_id
}

output "key_pair_name" {
  description = "Nome do key pair"
  value       = module.ec2.key_pair_name
}

output "localstack_endpoint" {
  description = "Endpoint LocalStack usado"
  value       = "${{ values.localstack_endpoint }}"
}

output "test_commands" {
  description = "Comandos para testar a instância (Padrão Engie)"
  value = {
    describe_instances = "aws --endpoint-url=${{ values.localstack_endpoint }} ec2 describe-instances"
    describe_security_groups = "aws --endpoint-url=${{ values.localstack_endpoint }} ec2 describe-security-groups"
    describe_key_pairs = "aws --endpoint-url=${{ values.localstack_endpoint }} ec2 describe-key-pairs"
    describe_vpcs = "aws --endpoint-url=${{ values.localstack_endpoint }} ec2 describe-vpcs"
  }
}

output "web_endpoints" {
  description = "Endpoints web da instância"
  value = {
    web_interface = "http://${module.ec2.instance_public_ip}/"
    ssh_access = "ssh -i ${module.ec2.key_pair_name}.pem ec2-user@${module.ec2.instance_public_ip}"
    custom_port = "http://${module.ec2.instance_public_ip}:8080/"
  }
}

output "ssh_command" {
  description = "Comando SSH para conectar (chave fictícia no LocalStack)"
  value       = "ssh -i ${aws_key_pair.main.key_name}.pem ec2-user@${aws_instance.main.public_ip}"
}

output "instance_info" {
  description = "Informações completas da instância (Padrão Engie)"
  value = {
    name = "${{ values.name }}"
    description = "${{ values.description }}"
    owner = "${{ values.owner }}"
    instance_type = "${{ values.instance_type }}"
    ami_id = "${{ values.ami_id }}"
    public_ip_enabled = "${{ values.enable_public_ip }}"
    region = "${{ values.aws_region }}"
    environment = "development"
    managed_by = "terraform"
    pattern = "engie"
  }
}
