#LOCALSTACK CONFIGURATION
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL"
  default     = "${{ values.localstack_endpoint }}"
  type        = string
}

variable "localstack_region" {
  description = "LocalStack simulated region"
  default     = "${{ values.localstack_region }}"
  type        = string
}

#INSTANCE CONFIGURATION
variable "instance_name" {
  description = "Name of the EC2 instance"
  default     = "${{ values.name }}"
  type        = string
}

variable "instance_type" {
  description = "EC2 instance type"
  default     = "${{ values.instance_type }}"
  type        = string
}

variable "ami_id" {
  description = "AMI ID for the instance"
  default     = "${{ values.ami_id }}"
  type        = string
}

variable "key_name" {
  description = "Name of the AWS key pair"
  default     = "${{ values.key_name }}"
  type        = string
}

variable "public_key" {
  description = "Public key for SSH access"
  default     = "${{ values.public_key }}"
  type        = string
}

variable "enable_public_ip" {
  description = "Associate public IP address"
  default     = ${{ values.enable_public_ip }}
  type        = bool
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
  }
}
