apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: localstack-ec2-${{values.name}}
  description: LocalStack EC2 Instance - ${{values.description}}
  annotations:
    github.com/project-slug: ${{values.destination.owner + "/" + values.destination.repo}}
    backstage.io/techdocs-ref: dir:.
  tags:
    - localstack
    - ec2
    - development
    - local
spec:
  type: service
  lifecycle: experimental
  owner: ${{values.owner}}
