#!/bin/bash

# Log all output
exec > >(tee /var/log/user-data.log|logger -t user-data -s 2>/dev/console) 2>&1

echo "Starting user data script for ${instance_name}"

# Update system
yum update -y

# Install basic tools
yum install -y wget curl unzip git

# Install Docker
yum install -y docker
systemctl start docker
systemctl enable docker
usermod -a -G docker ec2-user

# Install AWS CLI v2
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install

# Install Terraform
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
mv terraform /usr/local/bin/
rm terraform_1.6.0_linux_amd64.zip

# Create a simple web server for testing
yum install -y httpd
systemctl start httpd
systemctl enable httpd

# Create index page
cat > /var/www/html/index.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>${instance_name} - LocalStack EC2</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; background: #f0f0f0; }
        .container { max-width: 800px; margin: 0 auto; background: white; padding: 20px; border-radius: 10px; }
        .header { background: #232f3e; color: white; padding: 20px; border-radius: 5px; margin-bottom: 20px; }
        .info { background: #e8f4fd; padding: 15px; border-radius: 5px; margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ${instance_name}</h1>
            <p>LocalStack EC2 Instance - Development Environment</p>
        </div>
        
        <div class="info">
            <h3>Instance Information</h3>
            <p><strong>Instance Name:</strong> ${instance_name}</p>
            <p><strong>Environment:</strong> LocalStack Development</p>
            <p><strong>Deployed:</strong> $(date)</p>
            <p><strong>Managed by:</strong> Terraform + Backstage</p>
        </div>
        
        <div class="info">
            <h3>Available Tools</h3>
            <ul>
                <li>Docker</li>
                <li>AWS CLI v2</li>
                <li>Terraform</li>
                <li>Git</li>
                <li>Apache HTTP Server</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>LocalStack Configuration</h3>
            <p>This instance is configured to work with LocalStack for local AWS development.</p>
            <p>Use endpoint: <code>http://localhost:4566</code></p>
        </div>
    </div>
</body>
</html>
EOF

echo "User data script completed for ${instance_name}"
