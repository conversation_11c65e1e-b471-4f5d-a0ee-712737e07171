variable "bucket_name_prefix" {
  description = "Prefix for the S3 bucket name"
  type        = string
}

variable "bucket_purpose" {
  description = "Purpose of the S3 bucket"
  type        = string
}

variable "enable_versioning" {
  description = "Enable S3 bucket versioning"
  type        = bool
  default     = true
}

variable "enable_public_access" {
  description = "Enable public access to the bucket"
  type        = bool
  default     = false
}

variable "enable_encryption" {
  description = "Enable server-side encryption"
  type        = bool
  default     = true
}

variable "enable_lifecycle" {
  description = "Enable lifecycle management"
  type        = bool
  default     = true
}

variable "enable_access_logging" {
  description = "Enable access logging"
  type        = bool
  default     = false
}

variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default     = {}
}
