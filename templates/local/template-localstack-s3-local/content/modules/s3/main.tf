# Módulo S3 Local - Simula módulo Engie para LocalStack
# Este módulo replica a estrutura do módulo Engie iac-terraform-aws-s3

# Random suffix for bucket name uniqueness
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket
resource "aws_s3_bucket" "main" {
  bucket = "${var.bucket_name_prefix}-${random_string.bucket_suffix.result}"

  tags = merge(var.resource_tags, {
    Name = "${var.bucket_name_prefix}-${random_string.bucket_suffix.result}"
  })
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "main" {
  count  = var.enable_versioning ? 1 : 0
  bucket = aws_s3_bucket.main.id
  versioning_configuration {
    status = "Enabled"
  }
}

# S3 Bucket Public Access Block
resource "aws_s3_bucket_public_access_block" "main" {
  bucket = aws_s3_bucket.main.id

  block_public_acls       = !var.enable_public_access
  block_public_policy     = !var.enable_public_access
  ignore_public_acls      = !var.enable_public_access
  restrict_public_buckets = !var.enable_public_access
}

# S3 Bucket Server Side Encryption
resource "aws_s3_bucket_server_side_encryption_configuration" "main" {
  count  = var.enable_encryption ? 1 : 0
  bucket = aws_s3_bucket.main.id

  rule {
    apply_server_side_encryption_by_default {
      sse_algorithm = "AES256"
    }
  }
}

# S3 Bucket Lifecycle Configuration
resource "aws_s3_bucket_lifecycle_configuration" "main" {
  count  = var.enable_lifecycle ? 1 : 0
  bucket = aws_s3_bucket.main.id

  rule {
    id     = "lifecycle_rule"
    status = "Enabled"

    expiration {
      days = 90
    }

    noncurrent_version_expiration {
      noncurrent_days = 30
    }
  }
}

# S3 Bucket Logging
resource "aws_s3_bucket_logging" "main" {
  count  = var.enable_access_logging ? 1 : 0
  bucket = aws_s3_bucket.main.id

  target_bucket = aws_s3_bucket.main.id
  target_prefix = "access-logs/"
}

# Sample objects for testing (similar to Engie examples)
resource "aws_s3_object" "sample_readme" {
  bucket  = aws_s3_bucket.main.id
  key     = "README.md"
  content = <<EOF
# ${var.bucket_name_prefix} - S3 Bucket

## Informações
- **Bucket**: ${aws_s3_bucket.main.bucket}
- **Propósito**: ${var.bucket_purpose}
- **Versionamento**: ${var.enable_versioning}
- **Criptografia**: ${var.enable_encryption}
- **Ambiente**: LocalStack Development

## Padrão Engie
Este bucket segue o padrão Engie de nomenclatura e configuração.

## Comandos Úteis
```bash
# Listar objetos
aws --endpoint-url=http://localhost:4566 s3 ls s3://${aws_s3_bucket.main.bucket}/

# Upload arquivo
aws --endpoint-url=http://localhost:4566 s3 cp arquivo.txt s3://${aws_s3_bucket.main.bucket}/

# Download arquivo
aws --endpoint-url=http://localhost:4566 s3 cp s3://${aws_s3_bucket.main.bucket}/arquivo.txt .
```
EOF

  tags = var.resource_tags
}

resource "aws_s3_object" "sample_data" {
  bucket = aws_s3_bucket.main.id
  key    = "data/sample.json"
  content = jsonencode({
    bucket_name = aws_s3_bucket.main.bucket
    purpose     = var.bucket_purpose
    created_at  = timestamp()
    environment = "localstack"
    pattern     = "engie"
  })

  tags = var.resource_tags
}
