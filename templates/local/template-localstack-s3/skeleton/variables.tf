#LOCALSTACK CONFIGURATION
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL"
  default     = "${{ values.localstack_endpoint }}"
  type        = string
}

variable "localstack_region" {
  description = "LocalStack simulated region"
  default     = "${{ values.localstack_region }}"
  type        = string
}

#BUCKET CONFIGURATION
variable "bucket_name_prefix" {
  description = "Prefix for the S3 bucket name"
  default     = "${{ values.name }}"
  type        = string
}

variable "bucket_purpose" {
  description = "Purpose of the S3 bucket"
  default     = "${{ values.bucket_purpose }}"
  type        = string
}

#SECURITY CONFIGURATION
variable "enable_versioning" {
  description = "Enable S3 bucket versioning"
  default     = ${{ values.enable_versioning }}
  type        = bool
}

variable "enable_public_access" {
  description = "Enable public access to the bucket"
  default     = ${{ values.enable_public_access }}
  type        = bool
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Purpose     = "${{ values.bucket_purpose }}"
  }
}
