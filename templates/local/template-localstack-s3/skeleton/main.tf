# Random suffix for bucket name uniqueness
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket
resource "aws_s3_bucket" "main" {
  bucket = "${var.bucket_name_prefix}-${random_string.bucket_suffix.result}"

  tags = merge(var.resource_tags, {
    Name    = "${var.bucket_name_prefix}-${random_string.bucket_suffix.result}"
    Purpose = var.bucket_purpose
  })
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "main" {
  bucket = aws_s3_bucket.main.id
  versioning_configuration {
    status = var.enable_versioning ? "Enabled" : "Disabled"
  }
}

# S3 Bucket Public Access Block
resource "aws_s3_bucket_public_access_block" "main" {
  bucket = aws_s3_bucket.main.id

  block_public_acls       = !var.enable_public_access
  block_public_policy     = !var.enable_public_access
  ignore_public_acls      = !var.enable_public_access
  restrict_public_buckets = !var.enable_public_access
}

# S3 Bucket Policy for public access (if enabled)
resource "aws_s3_bucket_policy" "public_read" {
  count  = var.enable_public_access ? 1 : 0
  bucket = aws_s3_bucket.main.id

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid       = "PublicReadGetObject"
        Effect    = "Allow"
        Principal = "*"
        Action    = "s3:GetObject"
        Resource  = "${aws_s3_bucket.main.arn}/*"
      }
    ]
  })

  depends_on = [aws_s3_bucket_public_access_block.main]
}

# S3 Bucket Website Configuration (for static websites)
resource "aws_s3_bucket_website_configuration" "main" {
  count  = var.bucket_purpose == "static-website" ? 1 : 0
  bucket = aws_s3_bucket.main.id

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "error.html"
  }
}

# Sample object for testing
resource "aws_s3_object" "sample" {
  bucket = aws_s3_bucket.main.id
  key    = "sample.txt"
  content = "Hello from LocalStack S3! Bucket: ${aws_s3_bucket.main.bucket}"
  
  tags = var.resource_tags
}
