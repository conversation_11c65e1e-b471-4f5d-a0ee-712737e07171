output "bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.main.bucket
}

output "bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.main.arn
}

output "bucket_domain_name" {
  description = "Domain name of the S3 bucket"
  value       = aws_s3_bucket.main.bucket_domain_name
}

output "website_endpoint" {
  description = "Website endpoint (if static website hosting is enabled)"
  value       = var.bucket_purpose == "static-website" ? aws_s3_bucket_website_configuration.main[0].website_endpoint : null
}

output "localstack_endpoint" {
  description = "LocalStack endpoint used"
  value       = var.localstack_endpoint
}

output "sample_object_url" {
  description = "URL to access the sample object"
  value       = "${var.localstack_endpoint}/${aws_s3_bucket.main.bucket}/sample.txt"
}
