apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-terraform-rds
  title: Novo Banco RDS - Terraform
  description: Cria um código base em terraform para criação de bancos RDS na AWS
  tags:
    - rds
    - aws
    - database
spec:
  owner: backstage
  type: aws
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome do Banco RDS
          type: string
          description: Nome da instância do banco
          pattern: ^[a-zA-Z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição do Banco de Dados
        owner:
          title: GroupID
          type: string
          description: Responsável pelo Banco
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
    - title: Configurações do Banco
      required:
        - engine
        - instance_class
        - allocated_storage
        - username
      properties:
        engine:
          title: Engine do Banco
          type: string
          description: Tipo de banco de dados
          default: postgres
          enum:
            - mysql
            - postgres
            - mariadb
            - oracle-ee
            - sqlserver-ex
          enumNames:
            - MySQL
            - PostgreSQL
            - MariaDB
            - Oracle Enterprise
            - SQL Server Express
        instance_class:
          title: Classe da Instância
          type: string
          description: Tipo de instância RDS
          default: db.t3.micro
          enum:
            - db.t3.micro
            - db.t3.small
            - db.t3.medium
            - db.t3.large
            - db.r5.large
            - db.r5.xlarge
          enumNames:
            - db.t3.micro (1 vCPU, 1 GB RAM)
            - db.t3.small (2 vCPU, 2 GB RAM)
            - db.t3.medium (2 vCPU, 4 GB RAM)
            - db.t3.large (2 vCPU, 8 GB RAM)
            - db.r5.large (2 vCPU, 16 GB RAM)
            - db.r5.xlarge (4 vCPU, 32 GB RAM)
        allocated_storage:
          type: number
          title: Armazenamento (GB)
          description: Tamanho inicial do armazenamento
          default: 20
          minimum: 20
          maximum: 1000
        username:
          title: Usuário Master
          type: string
          description: Nome do usuário master do banco
          default: admin
          pattern: ^[a-zA-Z][a-zA-Z0-9]*$
        multi_az:
          title: Multi-AZ?
          type: boolean
          description: Implantar em múltiplas zonas de disponibilidade
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_backups:
          title: Habilitar Backups?
          type: boolean
          description: Habilitar backups automáticos
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_encryption:
          title: Habilitar Criptografia?
          type: boolean
          description: Habilitar criptografia em repouso
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        publicly_accessible:
          title: Acesso Público?
          type: boolean
          description: Permitir acesso público ao banco
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
      dependencies:
        enable_backups:
          oneOf:
            - properties:
                enable_backups:
                  enum:
                    - true
                backup_retention_period:
                  title: Período de Retenção (dias)
                  type: number
                  description: Dias para manter os backups
                  default: 7
                  minimum: 1
                  maximum: 35
                backup_window:
                  title: Janela de Backup
                  type: string
                  description: Horário para backup (UTC)
                  default: "03:00-04:00"
            - properties:
                enable_backups:
                  enum:
                    - false
    - title: Configurações de Rede
      properties:
        vpc_filter_key:
          title: Key da VPC
          type: string
          description: Key utilizada para buscar a VPC
          examples:
            - vpc-0ef416b78889d9295
            - vpc-096c613b72def76e1
        vpc_filter_tag:
          title: Tag da VPC
          type: string
          description: Tag utilizada para buscar a VPC
        subnet_group_name:
          title: Nome do Subnet Group
          type: string
          description: Nome do DB Subnet Group
        allowed_security_groups:
          title: Security Groups Permitidos
          type: array
          items:
            type: string
            examples:
              - sg-0123456789abcdef0
          ui:options:
            orderable: false
          description: Lista de Security Groups com acesso ao banco
    - title: Bitbucket
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repositório
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - almappro.tractebelenergia.com.br
            allowedOwners:
              - backstage
            allowedProjects:
              - BAC
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          engine: ${{ parameters.engine }}
          instance_class: ${{ parameters.instance_class }}
          allocated_storage: ${{ parameters.allocated_storage }}
          username: ${{ parameters.username }}
          multi_az: ${{ parameters.multi_az }}
          enable_backups: ${{ parameters.enable_backups }}
          backup_retention_period: ${{ parameters.backup_retention_period }}
          backup_window: ${{ parameters.backup_window }}
          enable_encryption: ${{ parameters.enable_encryption }}
          publicly_accessible: ${{ parameters.publicly_accessible }}
          vpc_filter_key: ${{ parameters.vpc_filter_key }}
          vpc_filter_tag: ${{ parameters.vpc_filter_tag }}
          subnet_group_name: ${{ parameters.subnet_group_name }}
          allowed_security_groups: ${{ parameters.allowed_security_groups }}
    - id: publish
      name: Publish
      action: publish:bitbucketServer
      input:
        allowedHosts:
          - almappro.tractebelenergia.com.br
        description: Componente ${{ parameters.name }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
