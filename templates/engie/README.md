# 🏢 Templates Terraform - Padrão Engie

Este diretório contém templates do Backstage seguindo o padrão específico da Engie para provisionamento de recursos AWS usando Terraform.

## 📦 Templates Disponíveis

### 🖥️ **EC2 Instance** - `template-terraform-ec2`
- **Descrição**: Cria máquinas EC2 com configurações personalizáveis
- **Recursos**: EC2, Security Group, EBS, User Data
- **Módulo**: `iac-terraform-aws-ec2.git`

### 🗄️ **S3 Bucket** - `template-terraform-s3`
- **Descrição**: Cria buckets S3 com configurações de segurança
- **Recursos**: S3 Bucket, Políticas, Lifecycle, Encryption
- **Módulo**: `iac-terraform-aws-s3.git`

### 🗃️ **RDS Database** - `template-terraform-rds`
- **Descrição**: Cria bancos de dados RDS gerenciados
- **Recursos**: RDS Instance, Subnet Group, Security Group
- **Módulo**: `iac-terraform-aws-rds.git`

### 🌐 **VPC** - `template-terraform-vpc`
- **Descrição**: Cria VPCs completas com subnets e gateways
- **Recursos**: VPC, Subnets, NAT Gateway, Route Tables
- **Módulo**: `iac-terraform-aws-vpc.git`

### ☸️ **EKS Cluster** - `template-terraform-eks`
- **Descrição**: Cria clusters Kubernetes gerenciados
- **Recursos**: EKS Cluster, Node Groups, IAM Roles
- **Módulo**: `iac-terraform-aws-eks.git`

## 🏗️ Padrão de Estrutura

Cada template segue o padrão Engie:

```
template-terraform-[recurso]/
├── template.yaml           # Definição do template Backstage
└── skeleton/              # Arquivos gerados pelo template
    ├── catalog-info.yaml  # Metadados do componente
    ├── modules.tf         # Chamada do módulo Terraform
    ├── provider.tf        # Configuração do provider AWS
    ├── variables.tf       # Variáveis de entrada
    ├── versions.tf        # Versões do Terraform
    └── config.sh          # Script de configuração (EC2)
```

## 🔧 Características do Padrão

### **Template.yaml**
- ✅ Metadados com tags específicas
- ✅ Parâmetros organizados por seções
- ✅ Validações e padrões de entrada
- ✅ Dependências condicionais
- ✅ Integração com Bitbucket Server
- ✅ OwnerPicker para grupos

### **Skeleton Files**
- ✅ Uso de módulos Terraform centralizados
- ✅ Provider AWS com tags padrão
- ✅ Variáveis com valores templated
- ✅ Versionamento específico do Terraform
- ✅ Catalog-info com lifecycle production

### **Configurações Específicas**
- 🏢 **Bitbucket**: `almappro.tractebelenergia.com.br`
- 📁 **Projeto**: `BAC`
- 👥 **Owner**: `backstage`
- 🏷️ **Lifecycle**: `production`

## 🚀 Como Usar

### 1. Via Interface Web
1. Acesse o Backstage
2. Vá para **Create** → **Choose a template**
3. Selecione um template Engie (prefixo `template-terraform-`)
4. Preencha as informações básicas
5. Configure os parâmetros específicos
6. Selecione o repositório Bitbucket
7. Clique em **Create**

### 2. Parâmetros Comuns

#### **Informações Básicas** (todos os templates)
- **Nome**: Nome do recurso (padrão alfanumérico)
- **Descrição**: Descrição do recurso
- **Owner**: Grupo responsável (OwnerPicker)

#### **Bitbucket** (todos os templates)
- **Repositório**: URL do repositório no Bitbucket Server

#### **Configurações Específicas**
Cada template tem suas próprias configurações:
- **EC2**: Tipo de instância, volume, rede, security group
- **S3**: Propósito, versionamento, criptografia, lifecycle
- **RDS**: Engine, classe, storage, backup, Multi-AZ
- **VPC**: CIDR, AZs, NAT Gateway, DNS, Flow Logs
- **EKS**: Versão K8s, node groups, logging, IRSA

## 🔒 Segurança e Compliance

### **Padrões de Segurança**
- ✅ Criptografia habilitada por padrão
- ✅ Acesso público restrito
- ✅ Backup automático (quando aplicável)
- ✅ Logging e monitoring
- ✅ Tags padronizadas

### **Governança**
- ✅ Módulos Terraform centralizados
- ✅ Versionamento controlado
- ✅ Aprovação via grupos
- ✅ Rastreabilidade completa

## 📋 Configuração no Backstage

Os templates estão configurados em:

```yaml
# app-config.yaml
catalog:
  locations:
    # Engie Templates
    - type: file
      target: ../../engie/template-terraform-ec2/template.yaml
      rules:
        - allow: [Template]
    
    - type: file
      target: ../../engie/template-terraform-s3/template.yaml
      rules:
        - allow: [Template]
    
    # ... outros templates
```

## 🛠️ Desenvolvimento

### **Criar Novo Template**
1. Copie um template existente:
   ```bash
   cp -r template-terraform-ec2 template-terraform-[novo-recurso]
   ```

2. Edite o `template.yaml`:
   - Altere `metadata.name`
   - Ajuste `title` e `description`
   - Configure `parameters` específicos
   - Mapeie `values` no step template

3. Adapte os arquivos `skeleton/`:
   - `modules.tf`: Chame o módulo correto
   - `variables.tf`: Defina variáveis necessárias
   - `catalog-info.yaml`: Ajuste metadados

4. Atualize a configuração:
   ```yaml
   # app-config.yaml
   - type: file
     target: ../../engie/template-terraform-[novo-recurso]/template.yaml
     rules:
       - allow: [Template]
   ```

### **Módulos Terraform**
Os templates referenciam módulos centralizados:
- `iac-terraform-aws-ec2.git`
- `iac-terraform-aws-s3.git`
- `iac-terraform-aws-rds.git`
- `iac-terraform-aws-vpc.git`
- `iac-terraform-aws-eks.git`

## 🐛 Troubleshooting

### **Template não aparece**
1. Verificar configuração em `app-config.yaml`
2. Restart do Backstage
3. Verificar sintaxe YAML

### **Erro no Bitbucket**
1. Verificar permissões no projeto BAC
2. Confirmar token de acesso
3. Validar URL do repositório

### **Erro no Terraform**
1. Verificar credenciais AWS
2. Validar módulo Terraform
3. Conferir permissões IAM

## 📚 Recursos

- **Bitbucket**: `http://almappro.tractebelenergia.com.br/bitbucket`
- **Módulos Terraform**: `scm/ect/iac-terraform-aws-*`
- **Documentação Backstage**: [backstage.io](https://backstage.io)

---

**Mantido por**: Equipe DevOps Engie  
**Última atualização**: $(date)  
**Versão**: 1.0
