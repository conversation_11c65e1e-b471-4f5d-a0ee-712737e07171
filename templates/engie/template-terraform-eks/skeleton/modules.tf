module "eks" {
  source = "git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-eks.git"

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## CLUSTER CONFIGURATION
  cluster_name       = var.cluster_name
  kubernetes_version = var.kubernetes_version
  
  ## LOGGING CONFIGURATION
  enable_cluster_logging = var.enable_cluster_logging
  
  ## IRSA CONFIGURATION
  enable_irsa = var.enable_irsa
  
  ## NODE GROUP CONFIGURATION
  node_group_instance_type = var.node_group_instance_type
  node_group_desired_size  = var.node_group_desired_size
  node_group_min_size      = var.node_group_min_size
  node_group_max_size      = var.node_group_max_size
  
  ## NETWORK CONFIGURATION
  vpc_filter_key    = var.vpc_filter_key
  vpc_filter        = var.vpc_filter
  subnet_filter_key = var.subnet_filter_key
  subnet_filter     = var.subnet_filter
  
  ## ENDPOINT CONFIGURATION
  endpoint_private_access = var.endpoint_private_access
  endpoint_public_access  = var.endpoint_public_access

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
