#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default = "${{ values.aws_region }}"
  type        = string
}

#CLUSTER CONFIGURATION
variable "cluster_name" {
  description = "Name of the E<PERSON> cluster"
  default = "${{ values.name }}"
  type        = string
}

variable "kubernetes_version" {
  description = "Kubernetes version for the EKS cluster"
  default = "${{ values.kubernetes_version }}"
  type        = string
}

#LOGGING CONFIGURATION
variable "enable_cluster_logging" {
  description = "Enable EKS cluster logging"
  default = ${{ values.enable_cluster_logging }}
  type        = bool
}

#IRSA CONFIGURATION
variable "enable_irsa" {
  description = "Enable IAM Roles for Service Accounts"
  default = ${{ values.enable_irsa }}
  type        = bool
}

#NODE GROUP CONFIGURATION
variable "node_group_instance_type" {
  description = "Instance type for the node group"
  default = "${{ values.node_group_instance_type }}"
  type        = string
}

variable "node_group_desired_size" {
  description = "Desired number of nodes in the node group"
  default = ${{ values.node_group_desired_size }}
  type        = number
}

variable "node_group_min_size" {
  description = "Minimum number of nodes in the node group"
  default = ${{ values.node_group_min_size }}
  type        = number
}

variable "node_group_max_size" {
  description = "Maximum number of nodes in the node group"
  default = ${{ values.node_group_max_size }}
  type        = number
}

#NETWORK CONFIGURATION
{% if values.vpc_filter_key %}
variable "vpc_filter_key" {
  description = "VPC key tag name"
  default = "${{ values.vpc_filter_key }}"
  type        = string
}
{% endif %}

{% if values.vpc_filter_tag %}
variable "vpc_filter" {
  description = "VPC value tag"
  default = "${{ values.vpc_filter_tag }}"
  type        = string
}
{% endif %}

{% if values.subnet_filter_key %}
variable "subnet_filter_key" {
  description = "Subnet key tag name"
  default = "${{ values.subnet_filter_key }}"
  type        = string
}
{% endif %}

{% if values.subnet_filter_tag %}
variable "subnet_filter" {
  description = "Subnet value tag"
  default = "${{ values.subnet_filter_tag }}"
  type        = string
}
{% endif %}

#ENDPOINT CONFIGURATION
variable "endpoint_private_access" {
  description = "Enable private API server endpoint"
  default = ${{ values.endpoint_private_access }}
  type        = bool
}

variable "endpoint_public_access" {
  description = "Enable public API server endpoint"
  default = ${{ values.endpoint_public_access }}
  type        = bool
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "production"
    ManagedBy   = "terraform"
    Cluster     = "${{ values.name }}"
  }
}
