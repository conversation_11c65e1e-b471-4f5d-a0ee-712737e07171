#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default = "${{ values.aws_region }}"
  type        = string
}

#VPC CONFIGURATION
variable "vpc_name" {
  description = "Name of the VPC"
  default = "${{ values.name }}"
  type        = string
}

variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  default = "${{ values.vpc_cidr }}"
  type        = string
}

#AVAILABILITY ZONES
variable "availability_zones" {
  description = "Number of availability zones to use"
  default = ${{ values.availability_zones }}
  type        = number
}

#DNS CONFIGURATION
variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  default = ${{ values.enable_dns_hostnames }}
  type        = bool
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  default = ${{ values.enable_dns_support }}
  type        = bool
}

#NAT GATEWAY CONFIGURATION
variable "enable_nat_gateway" {
  description = "Enable NAT Gateway for private subnets"
  default = ${{ values.enable_nat_gateway }}
  type        = bool
}

#SUBNET CONFIGURATION
variable "create_private_subnets" {
  description = "Create private subnets"
  default = ${{ values.create_private_subnets }}
  type        = bool
}

variable "create_database_subnets" {
  description = "Create database subnets"
  default = ${{ values.create_database_subnets }}
  type        = bool
}

#MONITORING CONFIGURATION
variable "enable_vpc_flow_logs" {
  description = "Enable VPC Flow Logs"
  default = ${{ values.enable_vpc_flow_logs }}
  type        = bool
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "production"
    ManagedBy   = "terraform"
    VPC         = "${{ values.name }}"
  }
}
