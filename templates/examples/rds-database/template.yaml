apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: rds-database-terraform
  title: AWS RDS Database with Terraform
  description: Deploy a managed AWS RDS database instance with security and backup configurations
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Basic Information
      required:
        - name
        - description
      properties:
        name:
          title: Database Name
          type: string
          description: Name for your RDS database
          ui:autofocus: true
          pattern: '^[a-zA-Z][a-zA-Z0-9]*$'
        description:
          title: Description
          type: string
          description: Purpose of this database
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
    - title: Database Configuration
      required:
        - region
        - engine
        - instanceClass
        - username
      properties:
        region:
          title: AWS Region
          type: string
          description: AWS region for the database
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - ap-southeast-1
          default: us-east-1
        engine:
          title: Database Engine
          type: string
          description: RDS database engine
          enum:
            - mysql
            - postgres
            - mariadb
            - oracle-ee
            - sqlserver-ex
          default: postgres
        instanceClass:
          title: Instance Class
          type: string
          description: RDS instance type
          enum:
            - db.t3.micro
            - db.t3.small
            - db.t3.medium
            - db.t3.large
            - db.r5.large
            - db.r5.xlarge
          default: db.t3.micro
        username:
          title: Master Username
          type: string
          description: Master username for the database
          default: admin
        allocatedStorage:
          title: Storage Size (GB)
          type: integer
          description: Initial storage allocation in GB
          default: 20
          minimum: 20
          maximum: 1000
        enableBackups:
          title: Enable Automated Backups
          type: boolean
          description: Enable automated daily backups
          default: true
        multiAZ:
          title: Multi-AZ Deployment
          type: boolean
          description: Deploy in multiple availability zones for high availability
          default: false
        enableEncryption:
          title: Enable Encryption
          type: boolean
          description: Enable encryption at rest
          default: true
  steps:
    - id: fetch-base
      name: Fetch Base
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          region: ${{ parameters.region }}
          engine: ${{ parameters.engine }}
          instanceClass: ${{ parameters.instanceClass }}
          username: ${{ parameters.username }}
          allocatedStorage: ${{ parameters.allocatedStorage }}
          enableBackups: ${{ parameters.enableBackups }}
          multiAZ: ${{ parameters.multiAZ }}
          enableEncryption: ${{ parameters.enableEncryption }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
