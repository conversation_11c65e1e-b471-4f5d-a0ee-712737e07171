apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: s3-bucket-terraform
  title: AWS S3 Bucket with Terraform
  description: Create a secure AWS S3 bucket with configurable features using Terraform
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Basic Information
      required:
        - name
        - description
      properties:
        name:
          title: Bucket Name Prefix
          type: string
          description: Prefix for your S3 bucket name (will be made unique)
          ui:autofocus: true
          pattern: '^[a-z0-9-]+$'
        description:
          title: Description
          type: string
          description: Purpose of this S3 bucket
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
    - title: S3 Configuration
      required:
        - region
        - bucketPurpose
      properties:
        region:
          title: AWS Region
          type: string
          description: AWS region for the S3 bucket
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - ap-southeast-1
          default: us-east-1
        bucketPurpose:
          title: Bucket Purpose
          type: string
          description: Primary use case for this bucket
          enum:
            - static-website
            - data-storage
            - backup
            - logs
            - media-assets
          default: data-storage
        enableVersioning:
          title: Enable Versioning
          type: boolean
          description: Enable object versioning
          default: true
        enableEncryption:
          title: Enable Encryption
          type: boolean
          description: Enable server-side encryption
          default: true
        enablePublicAccess:
          title: Enable Public Access
          type: boolean
          description: Allow public read access (for static websites)
          default: false
        lifecycleEnabled:
          title: Enable Lifecycle Rules
          type: boolean
          description: Enable automatic lifecycle management
          default: true
  steps:
    - id: fetch-base
      name: Fetch Base
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          region: ${{ parameters.region }}
          bucketPurpose: ${{ parameters.bucketPurpose }}
          enableVersioning: ${{ parameters.enableVersioning }}
          enableEncryption: ${{ parameters.enableEncryption }}
          enablePublicAccess: ${{ parameters.enablePublicAccess }}
          lifecycleEnabled: ${{ parameters.lifecycleEnabled }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
