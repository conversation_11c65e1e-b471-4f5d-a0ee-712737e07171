variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "${{ values.region }}"
}

variable "bucket_name_prefix" {
  description = "Prefix for the S3 bucket name"
  type        = string
  default     = "${{ values.name }}"
}

variable "bucket_purpose" {
  description = "Purpose of the S3 bucket"
  type        = string
  default     = "${{ values.bucketPurpose }}"
}

variable "enable_versioning" {
  description = "Enable S3 bucket versioning"
  type        = bool
  default     = ${{ values.enableVersioning }}
}

variable "enable_encryption" {
  description = "Enable S3 bucket encryption"
  type        = bool
  default     = ${{ values.enableEncryption }}
}

variable "enable_public_access" {
  description = "Enable public access to the bucket"
  type        = bool
  default     = ${{ values.enablePublicAccess }}
}

variable "lifecycle_enabled" {
  description = "Enable lifecycle management"
  type        = bool
  default     = ${{ values.lifecycleEnabled }}
}
