variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "${{ values.region }}"
}

variable "instance_type" {
  description = "EC2 instance type"
  type        = string
  default     = "${{ values.instanceType }}"
}

variable "key_pair_name" {
  description = "AWS Key Pair name for SSH access"
  type        = string
  default     = "${{ values.keyPairName }}"
}

variable "enable_public_ip" {
  description = "Whether to assign a public IP to the instance"
  type        = bool
  default     = ${{ values.enablePublicIP }}
}

variable "volume_size" {
  description = "Size of the root EBS volume in GB"
  type        = number
  default     = ${{ values.volumeSize }}
}
