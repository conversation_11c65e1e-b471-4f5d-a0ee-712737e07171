#!/bin/bash
yum update -y
yum install -y httpd

# Start and enable Apache
systemctl start httpd
systemctl enable httpd

# Create a simple index page
cat > /var/www/html/index.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>${instance_name} - AWS EC2 Instance</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .header { background: #232f3e; color: white; padding: 20px; border-radius: 5px; }
        .content { background: #f9f9f9; padding: 20px; border-radius: 5px; margin-top: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ${instance_name}</h1>
            <p>AWS EC2 Instance deployed with Terraform via Backstage</p>
        </div>
        <div class="content">
            <h2>Instance Information</h2>
            <p><strong>Instance Name:</strong> ${instance_name}</p>
            <p><strong>Deployed:</strong> $(date)</p>
            <p><strong>Managed by:</strong> Terraform + Backstage</p>
            
            <h3>Next Steps</h3>
            <ul>
                <li>Configure your application</li>
                <li>Set up monitoring and logging</li>
                <li>Implement security best practices</li>
                <li>Configure backup strategies</li>
            </ul>
        </div>
    </div>
</body>
</html>
EOF

# Set proper permissions
chown apache:apache /var/www/html/index.html
chmod 644 /var/www/html/index.html
