apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: ec2-instance-terraform
  title: AWS EC2 Instance with Terraform
  description: Deploy an AWS EC2 instance using Terraform with customizable configurations
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Basic Information
      required:
        - name
        - description
      properties:
        name:
          title: Instance Name
          type: string
          description: Name for your EC2 instance
          ui:autofocus: true
        description:
          title: Description
          type: string
          description: Brief description of the instance purpose
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
    - title: EC2 Configuration
      required:
        - instanceType
        - region
        - keyPairName
      properties:
        instanceType:
          title: Instance Type
          type: string
          description: EC2 instance type
          enum:
            - t3.micro
            - t3.small
            - t3.medium
            - t3.large
            - m5.large
            - m5.xlarge
          default: t3.micro
        region:
          title: AWS Region
          type: string
          description: AWS region for deployment
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - ap-southeast-1
          default: us-east-1
        keyPairName:
          title: Key Pair Name
          type: string
          description: AWS Key Pair name for SSH access
        enablePublicIP:
          title: Enable Public IP
          type: boolean
          description: Assign a public IP to the instance
          default: true
        volumeSize:
          title: Root Volume Size (GB)
          type: integer
          description: Size of the root EBS volume
          default: 20
          minimum: 8
          maximum: 100
  steps:
    - id: fetch-base
      name: Fetch Base
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          instanceType: ${{ parameters.instanceType }}
          region: ${{ parameters.region }}
          keyPairName: ${{ parameters.keyPairName }}
          enablePublicIP: ${{ parameters.enablePublicIP }}
          volumeSize: ${{ parameters.volumeSize }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
