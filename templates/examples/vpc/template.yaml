apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: vpc-terraform
  title: AWS VPC with Terraform
  description: Create a complete AWS VPC with public/private subnets, NAT Gateway, and Internet Gateway
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Basic Information
      required:
        - name
        - description
      properties:
        name:
          title: VPC Name
          type: string
          description: Name for your VPC
          ui:autofocus: true
        description:
          title: Description
          type: string
          description: Purpose of this VPC
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
    - title: VPC Configuration
      required:
        - region
        - vpcCidr
        - availabilityZones
      properties:
        region:
          title: AWS Region
          type: string
          description: AWS region for the VPC
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - ap-southeast-1
          default: us-east-1
        vpcCidr:
          title: VPC CIDR Block
          type: string
          description: CIDR block for the VPC
          enum:
            - 10.0.0.0/16
            - **********/16
            - ***********/16
          default: 10.0.0.0/16
        availabilityZones:
          title: Number of Availability Zones
          type: integer
          description: Number of AZs to use (2-3 recommended)
          enum:
            - 2
            - 3
          default: 2
        enableNatGateway:
          title: Enable NAT Gateway
          type: boolean
          description: Create NAT Gateway for private subnets
          default: true
        enableVpcFlowLogs:
          title: Enable VPC Flow Logs
          type: boolean
          description: Enable VPC Flow Logs for monitoring
          default: true
        enableDnsHostnames:
          title: Enable DNS Hostnames
          type: boolean
          description: Enable DNS hostnames in the VPC
          default: true
        enableDnsSupport:
          title: Enable DNS Support
          type: boolean
          description: Enable DNS resolution in the VPC
          default: true
  steps:
    - id: fetch-base
      name: Fetch Base
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          region: ${{ parameters.region }}
          vpcCidr: ${{ parameters.vpcCidr }}
          availabilityZones: ${{ parameters.availabilityZones }}
          enableNatGateway: ${{ parameters.enableNatGateway }}
          enableVpcFlowLogs: ${{ parameters.enableVpcFlowLogs }}
          enableDnsHostnames: ${{ parameters.enableDnsHostnames }}
          enableDnsSupport: ${{ parameters.enableDnsSupport }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps['publish'].output.repoContentsUrl }}
        catalogInfoPath: '/catalog-info.yaml'
  output:
    links:
      - title: Repository
        url: ${{ steps['publish'].output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps['register'].output.entityRef }}
