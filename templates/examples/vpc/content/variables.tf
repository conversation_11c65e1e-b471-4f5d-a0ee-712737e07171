variable "aws_region" {
  description = "AWS region for resources"
  type        = string
  default     = "${{ values.region }}"
}

variable "vpc_cidr" {
  description = "CIDR block for the VPC"
  type        = string
  default     = "${{ values.vpcCidr }}"
}

variable "availability_zones" {
  description = "Number of availability zones to use"
  type        = number
  default     = ${{ values.availabilityZones }}
}

variable "enable_nat_gateway" {
  description = "Enable NAT Gateway for private subnets"
  type        = bool
  default     = ${{ values.enableNatGateway }}
}

variable "enable_vpc_flow_logs" {
  description = "Enable VPC Flow Logs"
  type        = bool
  default     = ${{ values.enableVpcFlowLogs }}
}

variable "enable_dns_hostnames" {
  description = "Enable DNS hostnames in the VPC"
  type        = bool
  default     = ${{ values.enableDnsHostnames }}
}

variable "enable_dns_support" {
  description = "Enable DNS support in the VPC"
  type        = bool
  default     = ${{ values.enableDnsSupport }}
}
