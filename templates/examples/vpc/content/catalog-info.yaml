apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: ${{ values.name | dump }}
  description: ${{ values.description }}
  annotations:
    github.com/project-slug: ${{ values.destination.owner + "/" + values.destination.repo }}
    backstage.io/techdocs-ref: dir:.
  tags:
    - aws
    - vpc
    - networking
    - terraform
    - infrastructure
spec:
  type: service
  owner: user:guest
  lifecycle: experimental
