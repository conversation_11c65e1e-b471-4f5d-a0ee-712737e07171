# 🔧 Guia de Resolução de Problemas - Backstage + LocalStack

## ✅ **Problemas Resolvidos**

### **1. Node.js 20+ Error**
**Erro**: `When using Node.js version 20 or newer, the scaffolder backend plugin requires that it be started with the --no-node-snapshot option`

**✅ Solução Aplicada**:
- Atualizado `package.json` com `NODE_OPTIONS=--no-node-snapshot`
- <PERSON><PERSON><PERSON> script `start-backstage.sh` com configuração automática

### **2. BitbucketServer Action Not Found**
**Erro**: `Template action with ID 'publish:bitbucketServer' is not registered`

**✅ Solução Aplicada**:
- Templates LocalStack alterados para usar `publish:github`
- Configuração de repositório alterada para GitHub
- Owner alterado para `user:guest` e `type: service`

## 🚀 **Como Usar Agora**

### **1. Iniciar Backstage (Node.js 20+)**
```bash
# Método 1: Script automático
./start-backstage.sh

# Método 2: Manual
export NODE_OPTIONS="--no-node-snapshot"
yarn dev

# Método 3: Package.json (já configurado)
yarn dev  # Agora funciona automaticamente
```

### **2. Iniciar LocalStack**
```bash
# Terminal separado
docker run --rm -it -p 4566:4566 localstack/localstack

# Verificar se está funcionando
curl http://localhost:4566/health
```

### **3. Testar Templates LocalStack**
1. **Acesse**: http://localhost:3000
2. **Vá para**: Create → Choose a template
3. **Procure**: `LocalStack S3 Simple` (versão de teste)
4. **Preencha**: Nome do bucket e descrição
5. **Configure**: Repositório GitHub
6. **Crie**: Template

### **4. Aplicar Terraform**
```bash
# No repositório criado
terraform init
terraform plan
terraform apply

# Testar bucket
aws --endpoint-url=http://localhost:4566 s3 ls
```

## 🧪 **Templates Disponíveis**

### **✅ Funcionando (GitHub)**
- `template-localstack-s3-simple` - **Teste primeiro este!**
- `template-localstack-s3` - Versão completa
- `template-localstack-ec2` - Instância EC2
- `template-localstack-dynamodb` - Tabela DynamoDB

### **🏢 Padrão Engie (Bitbucket)**
- `template-terraform-ec2` - EC2 Engie
- `template-terraform-s3` - S3 Engie
- `template-terraform-rds` - RDS Engie
- `template-terraform-vpc` - VPC Engie
- `template-terraform-eks` - EKS Engie

## 🔍 **Troubleshooting Adicional**

### **LocalStack não responde**
```bash
# Verificar se está rodando
docker ps | grep localstack

# Verificar logs
docker logs $(docker ps -q --filter ancestor=localstack/localstack)

# Reiniciar
docker stop $(docker ps -q --filter ancestor=localstack/localstack)
docker run --rm -it -p 4566:4566 localstack/localstack
```

### **Template não aparece no Backstage**
```bash
# Verificar configuração
grep -A 5 "localstack" app-config.yaml

# Restart Backstage
yarn dev
```

### **Terraform falha**
```bash
# Verificar LocalStack
curl http://localhost:4566/health

# Verificar provider
terraform validate

# Debug
export TF_LOG=DEBUG
terraform apply
```

### **GitHub Authentication**
Se você não tem acesso ao GitHub, pode usar o template offline:

1. **Baixar skeleton** manualmente
2. **Copiar arquivos** para diretório local
3. **Aplicar terraform** diretamente

## 📋 **Checklist de Verificação**

### **Antes de Criar Template**
- [ ] LocalStack rodando (`curl http://localhost:4566/health`)
- [ ] Backstage rodando (`http://localhost:3000`)
- [ ] Node.js com flag (`NODE_OPTIONS=--no-node-snapshot`)
- [ ] GitHub token configurado (se necessário)

### **Após Criar Template**
- [ ] Repositório criado no GitHub
- [ ] Arquivos Terraform presentes
- [ ] `terraform init` executa sem erro
- [ ] `terraform plan` mostra recursos
- [ ] `terraform apply` cria recursos
- [ ] Recursos visíveis no LocalStack

## 🎯 **Próximos Passos**

### **1. Testar Template Simples**
Comece com `template-localstack-s3-simple` para validar o setup.

### **2. Expandir para Outros Recursos**
Após o S3 funcionar, teste EC2 e DynamoDB.

### **3. Configurar Bitbucket (Opcional)**
Para usar templates Engie, configure integração com Bitbucket Server.

### **4. Personalizar Templates**
Adapte templates conforme suas necessidades específicas.

## 📚 **Recursos Úteis**

- **LocalStack Health**: `http://localhost:4566/health`
- **Backstage**: `http://localhost:3000`
- **LocalStack Docs**: https://docs.localstack.cloud/
- **Backstage Docs**: https://backstage.io/docs/

## 🆘 **Se Ainda Não Funcionar**

1. **Verificar versões**:
   ```bash
   node --version  # Deve ser 20+
   yarn --version
   docker --version
   ```

2. **Limpar cache**:
   ```bash
   yarn cache clean
   rm -rf node_modules
   yarn install
   ```

3. **Logs detalhados**:
   ```bash
   export DEBUG=*
   yarn dev
   ```

4. **Usar template offline**:
   - Copiar arquivos `skeleton/` manualmente
   - Aplicar Terraform diretamente
   - Pular integração com Backstage temporariamente

---

**Status**: ✅ Problemas principais resolvidos  
**Próximo**: Testar `template-localstack-s3-simple`
