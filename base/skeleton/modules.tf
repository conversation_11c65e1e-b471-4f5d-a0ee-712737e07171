module "ec2" {
  source = "git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-ec2.git"

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## PROJECT CONFIGURATION
  ec2_template = var.ec2_template

  ## LAUNCH TEMPLATE CONFIGURATION
  amazon_linux_2            = var.amazon_linux_2
  instance_type             = var.instance_type
  device_name               = var.device_name
  volume_type               = var.volume_type
  volume_size               = var.volume_size
  ebs_delete_on_termination = var.ebs_delete_on_termination
  enabled_user_data         = var.enabled_user_data
  user_data                 = "${path.module}${var.user_data}"

  ## NETWORK CONFIGURATION
  vpc_filter_key            = var.vpc_filter_key
  vpc_filter                = var.vpc_filter
  private_subnet_filter_key = var.private_subnet_filter_key
  private_subnet_filter     = var.private_subnet_filter

  ## Security Group
  create_security_group = var.create_security_group
  ingress_ports         = var.ingress_ports
  ingress_cidrs         = var.ingress_cidrs

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}