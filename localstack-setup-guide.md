# 🧪 G<PERSON>a <PERSON>mpleto: LocalStack + Terraform + Backstage

## 🎯 Visão Geral

Este guia mostra como usar os templates LocalStack criados para desenvolvimento local de infraestrutura AWS sem custos.

## ✅ **RESPOSTA: SIM, você pode usar LocalStack com os templates criados!**

### **🏗️ Templates LocalStack Criados**

✅ **3 Templates Funcionais**:
1. **`template-localstack-s3`** - Bucket S3 local
2. **`template-localstack-ec2`** - Instância EC2 local  
3. **`template-localstack-dynamodb`** - Tabela DynamoDB local

### **🔧 Configuração Específica**
- ✅ **Provider AWS** configurado para LocalStack
- ✅ **Endpoints** apontando para `localhost:4566`
- ✅ **Credenciais mock** (`sample`/`sample`)
- ✅ **Padr<PERSON> Engie** mantido (Bitbucket, módulos, etc.)

## 🚀 Setup Rápido

### **1. Instalar LocalStack**
```bash
# Opção 1: Docker (recomendado)
docker pull localstack/localstack

# Opção 2: Python
pip install localstack

# Opção 3: Homebrew (macOS)
brew install localstack/tap/localstack-cli
```

### **2. Iniciar LocalStack**
```bash
# Comando simples
docker run --rm -it -p 4566:4566 localstack/localstack

# Com serviços específicos
docker run --rm -it -p 4566:4566 \
  -e SERVICES=s3,ec2,dynamodb,iam \
  localstack/localstack

# Verificar se está rodando
curl http://localhost:4566/health
```

### **3. Usar Templates no Backstage**
1. **Acesse**: Backstage → Create → Choose a template
2. **Procure**: `template-localstack-*`
3. **Configure**: Endpoint `http://localhost:4566`
4. **Crie**: Repositório com código Terraform

### **4. Aplicar Terraform**
```bash
# No repositório criado
terraform init
terraform plan
terraform apply

# Verificar recursos
aws --endpoint-url=http://localhost:4566 s3 ls
```

## 📋 Comparação: AWS Real vs LocalStack

| Aspecto | AWS Real | LocalStack |
|---------|----------|------------|
| **Custo** | 💰 Pago | 🆓 Gratuito |
| **Internet** | ✅ Necessária | ❌ Offline |
| **Velocidade** | 🐌 Rede | ⚡ Local |
| **Dados** | 🌐 Persistente | 💾 Temporário |
| **Serviços** | 🔥 Todos | 📦 Limitados |
| **Produção** | ✅ Sim | ❌ Não |

## 🔧 Configuração Detalhada

### **Docker Compose (Recomendado)**
```yaml
# docker-compose.yml
version: '3.8'
services:
  localstack:
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3,ec2,dynamodb,iam,cloudformation
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - "/tmp/localstack:/tmp/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
    networks:
      - localstack

networks:
  localstack:
    driver: bridge
```

```bash
# Iniciar
docker-compose up -d

# Parar
docker-compose down

# Logs
docker-compose logs -f localstack
```

### **AWS CLI para LocalStack**
```bash
# Configurar perfil
aws configure set aws_access_key_id sample --profile localstack
aws configure set aws_secret_access_key sample --profile localstack
aws configure set region us-east-1 --profile localstack

# Alias útil
alias awslocal="aws --endpoint-url=http://localhost:4566 --profile localstack"

# Usar
awslocal s3 ls
awslocal ec2 describe-instances
awslocal dynamodb list-tables
```

## 🧪 Exemplos de Uso

### **S3 Bucket**
```bash
# Criar bucket
awslocal s3 mb s3://meu-bucket-teste

# Upload arquivo
echo "Hello LocalStack" > test.txt
awslocal s3 cp test.txt s3://meu-bucket-teste/

# Listar objetos
awslocal s3 ls s3://meu-bucket-teste/

# Download arquivo
awslocal s3 cp s3://meu-bucket-teste/test.txt downloaded.txt
```

### **EC2 Instance**
```bash
# Listar instâncias
awslocal ec2 describe-instances

# Criar key pair
awslocal ec2 create-key-pair --key-name my-key

# Verificar security groups
awslocal ec2 describe-security-groups
```

### **DynamoDB**
```bash
# Listar tabelas
awslocal dynamodb list-tables

# Inserir item
awslocal dynamodb put-item \
  --table-name MinhaTabela \
  --item '{"id":{"S":"123"},"nome":{"S":"Teste"}}'

# Buscar item
awslocal dynamodb get-item \
  --table-name MinhaTabela \
  --key '{"id":{"S":"123"}}'

# Scan tabela
awslocal dynamodb scan --table-name MinhaTabela
```

## 🔍 Debugging e Troubleshooting

### **Verificar Status**
```bash
# Health check
curl http://localhost:4566/health

# Status detalhado
curl http://localhost:4566/_localstack/health

# Logs do container
docker logs $(docker ps -q --filter ancestor=localstack/localstack)
```

### **Problemas Comuns**

#### **LocalStack não inicia**
```bash
# Verificar porta ocupada
lsof -i :4566

# Matar processos
sudo kill -9 $(lsof -t -i:4566)

# Limpar containers
docker stop $(docker ps -q --filter ancestor=localstack/localstack)
docker rm $(docker ps -aq --filter ancestor=localstack/localstack)
```

#### **Terraform falha**
```bash
# Verificar conectividade
curl http://localhost:4566/health

# Validar configuração
terraform validate

# Debug Terraform
export TF_LOG=DEBUG
terraform apply
```

#### **AWS CLI não funciona**
```bash
# Testar conectividade
curl http://localhost:4566/health

# Verificar configuração
aws configure list --profile localstack

# Testar comando simples
awslocal sts get-caller-identity
```

## 📊 Limitações do LocalStack (Free)

### **✅ Suportado Completamente**
- S3, DynamoDB, Lambda, SQS, SNS
- CloudFormation, CloudWatch Logs
- IAM (básico), STS, SSM

### **⚠️ Suportado Parcialmente**
- EC2 (instâncias básicas)
- RDS (funcionalidade limitada)
- EKS (simulação básica)

### **❌ Não Suportado (Free)**
- ECS/Fargate avançado
- ElastiCache
- Redshift
- Recursos enterprise

## 🎯 Melhores Práticas

### **Desenvolvimento**
1. **Use LocalStack** para desenvolvimento inicial
2. **Teste localmente** antes de aplicar na AWS
3. **Mantenha scripts** de setup automatizado
4. **Documente diferenças** entre local e AWS

### **CI/CD**
```yaml
# GitHub Actions exemplo
name: Test with LocalStack
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    services:
      localstack:
        image: localstack/localstack
        ports:
          - 4566:4566
        env:
          SERVICES: s3,dynamodb,lambda
    
    steps:
      - uses: actions/checkout@v2
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v1
      
      - name: Test Infrastructure
        run: |
          terraform init
          terraform plan
          terraform apply -auto-approve
        env:
          AWS_ACCESS_KEY_ID: sample
          AWS_SECRET_ACCESS_KEY: sample
          AWS_DEFAULT_REGION: us-east-1
```

### **Scripts Úteis**
```bash
#!/bin/bash
# setup-localstack.sh

echo "🚀 Iniciando LocalStack..."
docker-compose up -d localstack

echo "⏳ Aguardando LocalStack..."
while ! curl -s http://localhost:4566/health > /dev/null; do
  sleep 1
done

echo "✅ LocalStack pronto!"
echo "🔗 Health: http://localhost:4566/health"
echo "🎛️ Dashboard: http://localhost:4566/_localstack/cockpit"

# Configurar AWS CLI
aws configure set aws_access_key_id sample --profile localstack
aws configure set aws_secret_access_key sample --profile localstack
aws configure set region us-east-1 --profile localstack

echo "🎯 Use: awslocal [comando]"
```

## 🎉 Conclusão

**✅ SIM! Você pode usar LocalStack perfeitamente com os templates criados!**

### **Benefícios Alcançados**
- 🆓 **Desenvolvimento gratuito** sem custos AWS
- ⚡ **Iterações rápidas** sem latência de rede
- 🔒 **Ambiente isolado** para testes seguros
- 🎯 **Padrão Engie mantido** com Bitbucket e governança

### **Templates Prontos**
- **S3**: Buckets com versionamento e políticas
- **EC2**: Instâncias com security groups e user data
- **DynamoDB**: Tabelas com itens de exemplo

### **Próximos Passos**
1. **Instalar LocalStack** seguindo o guia
2. **Testar templates** no Backstage
3. **Expandir para outros serviços** conforme necessário
4. **Integrar com CI/CD** para testes automatizados

Agora você tem um ambiente completo de desenvolvimento local que simula a AWS! 🚀

---

**Criado para**: Desenvolvimento Local  
**Tecnologias**: LocalStack + Terraform + Backstage  
**Padrão**: Engie
