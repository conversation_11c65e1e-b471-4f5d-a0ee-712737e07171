apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-localstack-ec2
  title: LocalStack EC2 Instance - Terraform
  description: Cria uma instância EC2 no LocalStack para desenvolvimento local
  tags:
    - ec2
    - localstack
    - development
    - local
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome da EC2
          type: string
          description: Nome da instância
          pattern: ^[a-zA-Z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição da instância EC2
        owner:
          title: Owner
          type: string
          description: Owner of the component
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
              - User
    - title: Configurações da Instância
      required:
        - instance_type
        - ami_id
      properties:
        instance_type:
          title: Tipo de Instância
          type: string
          description: Tipo de instância EC2
          default: t2.micro
          enum:
            - t2.micro
            - t2.small
            - t2.medium
            - t3.micro
            - t3.small
          enumNames:
            - t2.micro (1 vCPU, 1 GB RAM)
            - t2.small (1 vCPU, 2 GB RAM)
            - t2.medium (2 vCPU, 4 GB RAM)
            - t3.micro (2 vCPU, 1 GB RAM)
            - t3.small (2 vCPU, 2 GB RAM)
        ami_id:
          title: AMI ID
          type: string
          description: ID da AMI para a instância
          default: ami-0f29c8402f8cce65c
        key_name:
          title: Nome da Chave
          type: string
          description: Nome do key pair para SSH
          default: my-key-pair
        public_key:
          title: Chave Pública SSH
          type: string
          description: Chave pública SSH para acesso
          default: ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDZ6...
        enable_public_ip:
          title: Habilitar IP Público?
          type: boolean
          description: Associar IP público à instância
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações LocalStack
      properties:
        localstack_endpoint:
          title: Endpoint LocalStack
          type: string
          description: URL do endpoint LocalStack
          default: http://localhost:4566
        localstack_region:
          title: Região LocalStack
          type: string
          description: Região simulada no LocalStack
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          instance_type: ${{ parameters.instance_type }}
          ami_id: ${{ parameters.ami_id }}
          key_name: ${{ parameters.key_name }}
          public_key: ${{ parameters.public_key }}
          enable_public_ip: ${{ parameters.enable_public_ip }}
          localstack_endpoint: ${{ parameters.localstack_endpoint }}
          localstack_region: ${{ parameters.localstack_region }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
