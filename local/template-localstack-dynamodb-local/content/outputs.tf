output "table_name" {
  description = "Nome da tabela DynamoDB"
  value       = aws_dynamodb_table.main.name
}

output "table_arn" {
  description = "ARN da tabela DynamoDB"
  value       = aws_dynamodb_table.main.arn
}

output "table_id" {
  description = "ID da tabela DynamoDB"
  value       = aws_dynamodb_table.main.id
}

output "hash_key" {
  description = "Chave hash da tabela"
  value       = aws_dynamodb_table.main.hash_key
}

{% if values.range_key %}
output "range_key" {
  description = "Chave range da tabela"
  value       = aws_dynamodb_table.main.range_key
}
{% endif %}

output "billing_mode" {
  description = "Modo de cobrança da tabela"
  value       = aws_dynamodb_table.main.billing_mode
}

output "localstack_endpoint" {
  description = "Endpoint LocalStack usado"
  value       = "${{ values.localstack_endpoint }}"
}

output "test_commands" {
  description = "Comandos úteis para testar a tabela"
  value = {
    list_tables = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb list-tables"
    describe_table = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb describe-table --table-name ${{ values.name }}"
    scan_table = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb scan --table-name ${{ values.name }}"
    get_item = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb get-item --table-name ${{ values.name }} --key '{\"${{ values.hash_key }}\":{\"S\":\"sample-id-1\"}}'"
  }
}

output "sample_queries" {
  description = "Exemplos de queries para testar"
  value = {
    # Put item
    put_item = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb put-item --table-name ${{ values.name }} --item '{\"${{ values.hash_key }}\":{\"S\":\"novo-id\"},\"name\":{\"S\":\"Novo Item\"},\"active\":{\"BOOL\":true}}'"
    
    # Query (if range key exists)
    {% if values.range_key %}
    query = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb query --table-name ${{ values.name }} --key-condition-expression \"${{ values.hash_key }} = :pk\" --expression-attribute-values '{\":pk\":{\"S\":\"sample-id-1\"}}'"
    {% endif %}
    
    # Update item
    update_item = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb update-item --table-name ${{ values.name }} --key '{\"${{ values.hash_key }}\":{\"S\":\"sample-id-1\"}}' --update-expression \"SET #n = :val\" --expression-attribute-names '{\"#n\":\"name\"}' --expression-attribute-values '{\":val\":{\"S\":\"Nome Atualizado\"}}'"
    
    # Delete item
    delete_item = "aws --endpoint-url=${{ values.localstack_endpoint }} dynamodb delete-item --table-name ${{ values.name }} --key '{\"${{ values.hash_key }}\":{\"S\":\"sample-id-1\"}}'"
  }
}

output "table_info" {
  description = "Informações completas da tabela"
  value = {
    name = "${{ values.name }}"
    description = "${{ values.description }}"
    hash_key = "${{ values.hash_key }}"
    {% if values.range_key %}range_key = "${{ values.range_key }}"{% endif %}
    billing_mode = "${{ values.billing_mode }}"
    region = "${{ values.aws_region }}"
    sample_data_created = "${{ values.create_sample_data }}"
    endpoint = "${{ values.localstack_endpoint }}"
  }
}
