terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  access_key = "sample"
  secret_key = "sample"
  region     = "${{ values.aws_region }}"

  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    dynamodb = "${{ values.localstack_endpoint }}"
  }
}

# DynamoDB Table
resource "aws_dynamodb_table" "main" {
  name           = "${{ values.name }}"
  billing_mode   = "${{ values.billing_mode }}"
  hash_key       = "${{ values.hash_key }}"
  {% if values.range_key %}range_key      = "${{ values.range_key }}"{% endif %}

  # Hash key attribute
  attribute {
    name = "${{ values.hash_key }}"
    type = "S"  # String type
  }

  {% if values.range_key %}
  # Range key attribute
  attribute {
    name = "${{ values.range_key }}"
    type = "S"  # String type
  }
  {% endif %}

  tags = {
    Name        = "${{ values.name }}"
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Description = "${{ values.description }}"
  }
}

{% if values.create_sample_data %}
# Sample items for testing
resource "aws_dynamodb_table_item" "sample_item_1" {
  table_name = aws_dynamodb_table.main.name
  hash_key   = aws_dynamodb_table.main.hash_key
  {% if values.range_key %}range_key  = aws_dynamodb_table.main.range_key{% endif %}

  item = jsonencode({
    "${{ values.hash_key }}" = {
      S = "sample-id-1"
    }
    {% if values.range_key %}
    "${{ values.range_key }}" = {
      S = "sample-sort-1"
    }
    {% endif %}
    "name" = {
      S = "Primeiro Item de Exemplo"
    }
    "description" = {
      S = "Este é um item de exemplo criado automaticamente"
    }
    "category" = {
      S = "exemplo"
    }
    "created_at" = {
      S = timestamp()
    }
    "active" = {
      BOOL = true
    }
    "count" = {
      N = "1"
    }
  })
}

resource "aws_dynamodb_table_item" "sample_item_2" {
  table_name = aws_dynamodb_table.main.name
  hash_key   = aws_dynamodb_table.main.hash_key
  {% if values.range_key %}range_key  = aws_dynamodb_table.main.range_key{% endif %}

  item = jsonencode({
    "${{ values.hash_key }}" = {
      S = "sample-id-2"
    }
    {% if values.range_key %}
    "${{ values.range_key }}" = {
      S = "sample-sort-2"
    }
    {% endif %}
    "name" = {
      S = "Segundo Item de Exemplo"
    }
    "description" = {
      S = "Outro item para demonstrar a estrutura da tabela"
    }
    "category" = {
      S = "teste"
    }
    "created_at" = {
      S = timestamp()
    }
    "active" = {
      BOOL = true
    }
    "count" = {
      N = "2"
    }
  })
}

resource "aws_dynamodb_table_item" "sample_item_3" {
  table_name = aws_dynamodb_table.main.name
  hash_key   = aws_dynamodb_table.main.hash_key
  {% if values.range_key %}range_key  = aws_dynamodb_table.main.range_key{% endif %}

  item = jsonencode({
    "${{ values.hash_key }}" = {
      S = "sample-id-3"
    }
    {% if values.range_key %}
    "${{ values.range_key }}" = {
      S = "sample-sort-3"
    }
    {% endif %}
    "name" = {
      S = "Terceiro Item de Exemplo"
    }
    "description" = {
      S = "Item com dados mais complexos para teste"
    }
    "category" = {
      S = "avançado"
    }
    "created_at" = {
      S = timestamp()
    }
    "active" = {
      BOOL = false
    }
    "count" = {
      N = "3"
    }
    "metadata" = {
      M = {
        "version" = {
          S = "1.0"
        }
        "source" = {
          S = "localstack-terraform"
        }
      }
    }
    "tags" = {
      SS = ["exemplo", "teste", "localstack"]
    }
  })
}
{% endif %}
