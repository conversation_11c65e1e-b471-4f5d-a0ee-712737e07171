apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-localstack-dynamodb-local
  title: LocalStack DynamoDB Local - Terraform
  description: Gera arquivos Terraform para DynamoDB LocalStack (sem repositório)
  tags:
    - dynamodb
    - localstack
    - development
    - local
    - offline
    - database
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Configurações da Tabela DynamoDB
      required:
        - name
        - description
        - hash_key
      properties:
        name:
          title: Nome da Tabela
          type: string
          description: Nome da tabela DynamoDB
          pattern: ^[a-zA-Z0-9\-_]*$
          maxLength: 50
          ui:autofocus: true
          default: MinhaTabela
        description:
          title: Descrição
          type: string
          description: Descrição da tabela
          default: Tabela DynamoDB para testes LocalStack
        hash_key:
          title: Chave <PERSON> (Partition Key)
          type: string
          description: Nome do atributo da chave de partição
          default: id
        range_key:
          title: Chave Range (Sort Key)
          type: string
          description: Nome do atributo da chave de ordenação (opcional)
        billing_mode:
          title: Modo de Cobrança
          type: string
          description: Modo de cobrança da tabela
          default: PAY_PER_REQUEST
          enum:
            - PAY_PER_REQUEST
            - PROVISIONED
          enumNames:
            - Pay Per Request (On-Demand)
            - Provisioned Throughput
        create_sample_data:
          title: Criar Dados de Exemplo?
          type: boolean
          description: Inserir dados de exemplo na tabela
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações LocalStack
      properties:
        localstack_endpoint:
          title: Endpoint LocalStack
          type: string
          description: URL do endpoint LocalStack
          default: http://localhost:4566
        aws_region:
          title: Região AWS
          type: string
          description: Região simulada
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
  steps:
    - id: template
      name: Generate Files
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          hash_key: ${{ parameters.hash_key }}
          range_key: ${{ parameters.range_key }}
          billing_mode: ${{ parameters.billing_mode }}
          create_sample_data: ${{ parameters.create_sample_data }}
          localstack_endpoint: ${{ parameters.localstack_endpoint }}
          aws_region: ${{ parameters.aws_region }}
        targetPath: ./localstack-dynamodb-${{ parameters.name }}
    
    - id: log
      name: Log Information
      action: debug:log
      input:
        message: |
          🎉 Arquivos Terraform gerados com sucesso!
          
          📁 Localização: ./localstack-dynamodb-${{ parameters.name }}
          
          🚀 Para usar:
          1. cd localstack-dynamodb-${{ parameters.name }}
          2. terraform init
          3. terraform plan
          4. terraform apply
          
          🧪 Para testar:
          aws --endpoint-url=${{ parameters.localstack_endpoint }} dynamodb list-tables
          
          📋 Certifique-se que LocalStack está rodando:
          docker run --rm -it -p 4566:4566 localstack/localstack

  output:
    text:
      - title: Arquivos gerados em
        content: ./localstack-dynamodb-${{ parameters.name }}
      - title: Próximos passos
        content: |
          cd localstack-dynamodb-${{ parameters.name }}
          terraform init && terraform apply
