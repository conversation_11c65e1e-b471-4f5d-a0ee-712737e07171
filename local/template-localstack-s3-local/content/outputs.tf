output "bucket_name" {
  description = "Nome do bucket S3 criado"
  value       = aws_s3_bucket.main.bucket
}

output "bucket_arn" {
  description = "ARN do bucket S3"
  value       = aws_s3_bucket.main.arn
}

output "localstack_endpoint" {
  description = "Endpoint LocalStack usado"
  value       = "${{ values.localstack_endpoint }}"
}

output "test_commands" {
  description = "Comandos para testar o bucket"
  value = {
    list_buckets = "aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls"
    list_objects = "aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls s3://${aws_s3_bucket.main.bucket}/"
    download_sample = "aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp s3://${aws_s3_bucket.main.bucket}/sample.txt ."
    upload_test = "echo 'test' > test.txt && aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp test.txt s3://${aws_s3_bucket.main.bucket}/"
  }
}

output "bucket_info" {
  description = "Informações do bucket"
  value = {
    name = aws_s3_bucket.main.bucket
    purpose = "${{ values.bucket_purpose }}"
    versioning = "${{ values.enable_versioning }}"
    region = "${{ values.aws_region }}"
    sample_objects = [
      "sample.txt",
      "README.md", 
      "test-data/info.json"
    ]
  }
}
