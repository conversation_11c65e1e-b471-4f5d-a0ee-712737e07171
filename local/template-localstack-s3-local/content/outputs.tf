output "bucket_name" {
  description = "Nome do bucket S3 criado"
  value       = module.s3.bucket_name
}

output "bucket_arn" {
  description = "ARN do bucket S3"
  value       = module.s3.bucket_arn
}

output "bucket_id" {
  description = "ID do bucket S3"
  value       = module.s3.bucket_id
}

output "localstack_endpoint" {
  description = "Endpoint LocalStack usado"
  value       = "${{ values.localstack_endpoint }}"
}

output "test_commands" {
  description = "Comandos para testar o bucket"
  value = {
    list_buckets = "aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls"
    list_objects = "aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls s3://${module.s3.bucket_name}/"
    download_sample = "aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp s3://${module.s3.bucket_name}/README.md ."
    upload_test = "echo 'test' > test.txt && aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp test.txt s3://${module.s3.bucket_name}/"
  }
}

output "bucket_info" {
  description = "Informações do bucket (Padrão Engie)"
  value = {
    name = module.s3.bucket_name
    purpose = "${{ values.bucket_purpose }}"
    versioning = "${{ values.enable_versioning }}"
    encryption = "true"
    region = "${{ values.aws_region }}"
    environment = "development"
    managed_by = "terraform"
    pattern = "engie"
    sample_objects = [
      "README.md",
      "data/sample.json"
    ]
  }
}
