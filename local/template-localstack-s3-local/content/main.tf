terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "aws" {
  access_key = "sample"
  secret_key = "sample"
  region     = "${{ values.aws_region }}"

  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    s3 = "${{ values.localstack_endpoint }}"
  }
}

# Random suffix for bucket name uniqueness
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket
resource "aws_s3_bucket" "main" {
  bucket = "${{ values.name }}-${random_string.bucket_suffix.result}"

  tags = {
    Name        = "${{ values.name }}-${random_string.bucket_suffix.result}"
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Purpose     = "${{ values.bucket_purpose }}"
  }
}

# S3 Bucket Versioning
resource "aws_s3_bucket_versioning" "main" {
  bucket = aws_s3_bucket.main.id
  versioning_configuration {
    status = "${{ values.enable_versioning }}" == "true" ? "Enabled" : "Disabled"
  }
}

# S3 Bucket Public Access Block
resource "aws_s3_bucket_public_access_block" "main" {
  bucket = aws_s3_bucket.main.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}

# Sample objects for testing
resource "aws_s3_object" "sample_txt" {
  bucket  = aws_s3_bucket.main.id
  key     = "sample.txt"
  content = "Hello from LocalStack S3!\nBucket: ${aws_s3_bucket.main.bucket}\nPurpose: ${{ values.bucket_purpose }}\nCreated: ${timestamp()}"
}

resource "aws_s3_object" "readme" {
  bucket  = aws_s3_bucket.main.id
  key     = "README.md"
  content = <<EOF
# ${{ values.name }} - LocalStack S3 Bucket

## Informações
- **Bucket**: ${aws_s3_bucket.main.bucket}
- **Propósito**: ${{ values.bucket_purpose }}
- **Versionamento**: ${{ values.enable_versioning }}
- **Endpoint**: ${{ values.localstack_endpoint }}

## Comandos Úteis

### Listar buckets
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls
```

### Listar objetos
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls s3://${aws_s3_bucket.main.bucket}/
```

### Upload arquivo
```bash
echo "Meu arquivo teste" > test.txt
aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp test.txt s3://${aws_s3_bucket.main.bucket}/
```

### Download arquivo
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp s3://${aws_s3_bucket.main.bucket}/sample.txt ./downloaded.txt
```

### Sync diretório
```bash
aws --endpoint-url=${{ values.localstack_endpoint }} s3 sync ./meu-diretorio s3://${aws_s3_bucket.main.bucket}/uploads/
```
EOF
}

# Test data folder structure
resource "aws_s3_object" "test_folder" {
  bucket = aws_s3_bucket.main.id
  key    = "test-data/info.json"
  content = jsonencode({
    bucket_name = aws_s3_bucket.main.bucket
    created_at  = timestamp()
    purpose     = "${{ values.bucket_purpose }}"
    localstack  = true
  })
}
