#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default     = "${{ values.aws_region }}"
  type        = string
}

#BUCKET CONFIGURATION
variable "bucket_name_prefix" {
  description = "Prefix for the S3 bucket name"
  default     = "${{ values.name }}"
  type        = string
}

variable "bucket_purpose" {
  description = "Purpose of the S3 bucket"
  default     = "${{ values.bucket_purpose }}"
  type        = string
}

#SECURITY CONFIGURATION
variable "enable_versioning" {
  description = "Enable S3 bucket versioning"
  default     = ${{ values.enable_versioning }}
  type        = bool
}

variable "enable_public_access" {
  description = "Enable public access to the bucket"
  default     = false
  type        = bool
}

variable "enable_encryption" {
  description = "Enable server-side encryption"
  default     = true
  type        = bool
}

#LIFECYCLE CONFIGURATION
variable "enable_lifecycle" {
  description = "Enable lifecycle management"
  default     = true
  type        = bool
}

#LOGGING CONFIGURATION
variable "enable_access_logging" {
  description = "Enable access logging"
  default     = false
  type        = bool
}

#LOCALSTACK CONFIGURATION
variable "localstack_endpoint" {
  description = "LocalStack endpoint URL"
  default     = "${{ values.localstack_endpoint }}"
  type        = string
}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Purpose     = "${{ values.bucket_purpose }}"
    Owner       = "${{ values.owner }}"
  }
}
