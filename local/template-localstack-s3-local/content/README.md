# S3 Bucket - ${{ values.name }} (Padrão Engie + LocalStack)

Bucket S3 criado seguindo o padrão Engie para desenvolvimento local com LocalStack.

## 📋 Informações

- **Nome**: ${{ values.name }}
- **Descrição**: ${{ values.description }}
- **Owner**: ${{ values.owner }}
- **Propósito**: ${{ values.bucket_purpose }}
- **Versionamento**: ${{ values.enable_versioning }}
- **Criptografia**: Habilitada (AES256)
- **Endpoint**: ${{ values.localstack_endpoint }}
- **Região**: ${{ values.aws_region }}
- **Padrão**: Engie + LocalStack

## 🏢 Padrão Engie

Este bucket segue o padrão Engie de infraestrutura:

- ✅ **<PERSON><PERSON><PERSON><PERSON> Terraform** centralizados
- ✅ **Tags padronizadas** (Environment, ManagedBy, Owner)
- ✅ **Versionamento** controlado
- ✅ **Criptografia** por padrão
- ✅ **Nomenclatura** seguindo convenções
- ✅ **Lifecycle** de produção

## 🚀 Como Usar

### 1. Iniciar LocalStack

```bash
docker run --rm -it -p 4566:4566 localstack/localstack
```

### 2. Aplicar Terraform

```bash
terraform init
terraform plan
terraform apply
```

### 3. Testar Bucket

```bash
# Listar buckets
aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls

# Listar objetos
aws --endpoint-url=${{ values.localstack_endpoint }} s3 ls s3://[BUCKET_NAME]/

# Download arquivo de exemplo
aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp s3://[BUCKET_NAME]/sample.txt .
```

## 📁 Estrutura Criada

O bucket será criado com os seguintes objetos de exemplo:

- `sample.txt` - Arquivo de texto simples
- `README.md` - Documentação do bucket
- `test-data/info.json` - Dados JSON de teste

## 🧪 Comandos de Teste

### Upload de arquivo

```bash
echo "Meu arquivo teste" > test.txt
aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp test.txt s3://[BUCKET_NAME]/
```

### Sync de diretório

```bash
mkdir meus-arquivos
echo "arquivo 1" > meus-arquivos/file1.txt
echo "arquivo 2" > meus-arquivos/file2.txt
aws --endpoint-url=${{ values.localstack_endpoint }} s3 sync meus-arquivos s3://[BUCKET_NAME]/uploads/
```

### Verificar versionamento (se habilitado)

```bash
# Upload nova versão do mesmo arquivo
echo "versão 2" > test.txt
aws --endpoint-url=${{ values.localstack_endpoint }} s3 cp test.txt s3://[BUCKET_NAME]/

# Listar versões
aws --endpoint-url=${{ values.localstack_endpoint }} s3api list-object-versions --bucket [BUCKET_NAME]
```

## 🔧 Troubleshooting

### LocalStack não responde

```bash
# Verificar se está rodando
curl ${{ values.localstack_endpoint }}/health

# Ver logs
docker logs $(docker ps -q --filter ancestor=localstack/localstack)
```

### Terraform falha

```bash
# Validar configuração
terraform validate

# Debug
export TF_LOG=DEBUG
terraform apply
```

## 🎯 Próximos Passos

1. Teste os comandos básicos
2. Experimente upload/download de arquivos
3. Explore as funcionalidades do S3
4. Adapte conforme suas necessidades

---

**Criado com**: Backstage + LocalStack  
**Ambiente**: Desenvolvimento Local
