# Módulo S3 seguindo padrão Engie
# Em produção: git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-s3.git
# Para LocalStack: módulo local simulando o comportamento

module "s3" {
  source = "./modules/s3"  # Módulo local para LocalStack

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## BUCKET CONFIGURATION
  bucket_name_prefix = var.bucket_name_prefix
  bucket_purpose     = var.bucket_purpose
  
  ## SECURITY CONFIGURATION
  enable_versioning    = var.enable_versioning
  enable_public_access = var.enable_public_access
  enable_encryption    = var.enable_encryption
  
  ## LIFECYCLE CONFIGURATION
  enable_lifecycle = var.enable_lifecycle
  
  ## LOGGING CONFIGURATION
  enable_access_logging = var.enable_access_logging

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
