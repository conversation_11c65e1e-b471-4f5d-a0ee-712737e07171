apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-localstack-s3-local
  title: LocalStack S3 Local - Terraform
  description: Gera arquivos Terraform para S3 LocalStack (sem repositório)
  tags:
    - s3
    - localstack
    - development
    - local
    - offline
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Configurações do Bucket S3
      required:
        - name
        - description
      properties:
        name:
          title: Nome do Bucket S3
          type: string
          description: Nome do bucket (será adicionado sufixo único)
          pattern: ^[a-z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
          default: meu-bucket-teste
        description:
          title: Descrição
          type: string
          description: Descrição do Bucket S3
          default: Bucket S3 para testes LocalStack
        enable_versioning:
          title: Habilitar Versionamento?
          type: boolean
          description: Habilitar versionamento de objetos
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        bucket_purpose:
          title: Propósito do Bucket
          type: string
          description: Finalidade principal do bucket
          default: data-storage
          enum:
            - data-storage
            - static-website
            - backup
            - logs
          enumNames:
            - Armazenamento de Dados
            - Site Estático
            - Backup
            - Logs
    - title: Configurações LocalStack
      properties:
        localstack_endpoint:
          title: Endpoint LocalStack
          type: string
          description: URL do endpoint LocalStack
          default: http://localhost:4566
        aws_region:
          title: Região AWS
          type: string
          description: Região simulada
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
  steps:
    - id: template
      name: Generate Files
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          enable_versioning: ${{ parameters.enable_versioning }}
          bucket_purpose: ${{ parameters.bucket_purpose }}
          localstack_endpoint: ${{ parameters.localstack_endpoint }}
          aws_region: ${{ parameters.aws_region }}
        targetPath: ./localstack-s3-${{ parameters.name }}
    
    - id: log
      name: Log Information
      action: debug:log
      input:
        message: |
          🎉 Arquivos Terraform gerados com sucesso!
          
          📁 Localização: ./localstack-s3-${{ parameters.name }}
          
          🚀 Para usar:
          1. cd localstack-s3-${{ parameters.name }}
          2. terraform init
          3. terraform plan
          4. terraform apply
          
          🧪 Para testar:
          aws --endpoint-url=${{ parameters.localstack_endpoint }} s3 ls
          
          📋 Certifique-se que LocalStack está rodando:
          docker run --rm -it -p 4566:4566 localstack/localstack

  output:
    text:
      - title: Arquivos gerados em
        content: ./localstack-s3-${{ parameters.name }}
      - title: Próximos passos
        content: |
          cd localstack-s3-${{ parameters.name }}
          terraform init && terraform apply
