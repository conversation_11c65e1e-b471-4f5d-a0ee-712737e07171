apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-localstack-ec2-local
  title: LocalStack EC2 Local - Terraform
  description: Gera arquivos Terraform para EC2 LocalStack (sem repositório)
  tags:
    - ec2
    - localstack
    - development
    - local
    - offline
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Configurações da Instância EC2
      required:
        - name
        - description
      properties:
        name:
          title: Nome da Instância
          type: string
          description: Nome da instância EC2
          pattern: ^[a-zA-Z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
          default: minha-instancia-teste
        description:
          title: Descrição
          type: string
          description: Descrição da instância
          default: Instância EC2 para testes LocalStack
        instance_type:
          title: Tipo de Instância
          type: string
          description: Tipo de instância EC2
          default: t2.micro
          enum:
            - t2.micro
            - t2.small
            - t2.medium
            - t3.micro
            - t3.small
          enumNames:
            - t2.micro (1 vCPU, 1 GB RAM)
            - t2.small (1 vCPU, 2 GB RAM)
            - t2.medium (2 vCPU, 4 GB RAM)
            - t3.micro (2 vCPU, 1 GB RAM)
            - t3.small (2 vCPU, 2 GB RAM)
        ami_id:
          title: AMI ID
          type: string
          description: ID da AMI (Amazon Linux 2)
          default: ami-0f29c8402f8cce65c
        enable_public_ip:
          title: IP Público?
          type: boolean
          description: Associar IP público
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações LocalStack
      properties:
        localstack_endpoint:
          title: Endpoint LocalStack
          type: string
          description: URL do endpoint LocalStack
          default: http://localhost:4566
        aws_region:
          title: Região AWS
          type: string
          description: Região simulada
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
  steps:
    - id: template
      name: Generate Files
      action: fetch:template
      input:
        url: ./content
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          instance_type: ${{ parameters.instance_type }}
          ami_id: ${{ parameters.ami_id }}
          enable_public_ip: ${{ parameters.enable_public_ip }}
          localstack_endpoint: ${{ parameters.localstack_endpoint }}
          aws_region: ${{ parameters.aws_region }}
        targetPath: ./localstack-ec2-${{ parameters.name }}
    
    - id: log
      name: Log Information
      action: debug:log
      input:
        message: |
          🎉 Arquivos Terraform gerados com sucesso!
          
          📁 Localização: ./localstack-ec2-${{ parameters.name }}
          
          🚀 Para usar:
          1. cd localstack-ec2-${{ parameters.name }}
          2. terraform init
          3. terraform plan
          4. terraform apply
          
          🧪 Para testar:
          aws --endpoint-url=${{ parameters.localstack_endpoint }} ec2 describe-instances
          
          📋 Certifique-se que LocalStack está rodando:
          docker run --rm -it -p 4566:4566 localstack/localstack

  output:
    text:
      - title: Arquivos gerados em
        content: ./localstack-ec2-${{ parameters.name }}
      - title: Próximos passos
        content: |
          cd localstack-ec2-${{ parameters.name }}
          terraform init && terraform apply
