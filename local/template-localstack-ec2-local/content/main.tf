terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
}

provider "aws" {
  access_key = "sample"
  secret_key = "sample"
  region     = "${{ values.aws_region }}"

  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    ec2 = "${{ values.localstack_endpoint }}"
  }
}

# Default VPC (LocalStack creates one automatically)
data "aws_vpc" "default" {
  default = true
}

# Default subnet
data "aws_subnets" "default" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.default.id]
  }
}

# Key Pair
resource "aws_key_pair" "main" {
  key_name   = "${{ values.name }}-key"
  public_key = "ssh-rsa AAAAB3NzaC1yc2EAAAADAQABAAABAQDZ6+sample+key+for+localstack+testing"

  tags = {
    Name        = "${{ values.name }}-key"
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
  }
}

# Security Group
resource "aws_security_group" "main" {
  name_prefix = "${{ values.name }}-sg"
  description = "Security group for ${{ values.name }} LocalStack instance"
  vpc_id      = data.aws_vpc.default.id

  # SSH access
  ingress {
    description = "SSH"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTP access
  ingress {
    description = "HTTP"
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # HTTPS access
  ingress {
    description = "HTTPS"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Custom port for testing
  ingress {
    description = "Custom App"
    from_port   = 8080
    to_port     = 8080
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # All outbound traffic
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "${{ values.name }}-sg"
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
  }
}

# EC2 Instance
resource "aws_instance" "main" {
  ami                    = "${{ values.ami_id }}"
  instance_type          = "${{ values.instance_type }}"
  key_name              = aws_key_pair.main.key_name
  vpc_security_group_ids = [aws_security_group.main.id]
  subnet_id             = data.aws_subnets.default.ids[0]
  
  associate_public_ip_address = ${{ values.enable_public_ip }}

  user_data = base64encode(templatefile("${path.module}/user_data.sh", {
    instance_name = "${{ values.name }}"
    description   = "${{ values.description }}"
  }))

  tags = {
    Name        = "${{ values.name }}"
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
    Description = "${{ values.description }}"
  }
}
