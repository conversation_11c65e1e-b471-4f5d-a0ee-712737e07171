output "instance_id" {
  description = "ID da instância EC2"
  value       = aws_instance.main.id
}

output "instance_public_ip" {
  description = "IP público da instância"
  value       = aws_instance.main.public_ip
}

output "instance_private_ip" {
  description = "IP privado da instância"
  value       = aws_instance.main.private_ip
}

output "security_group_id" {
  description = "ID do security group"
  value       = aws_security_group.main.id
}

output "key_pair_name" {
  description = "Nome do key pair"
  value       = aws_key_pair.main.key_name
}

output "localstack_endpoint" {
  description = "Endpoint LocalStack usado"
  value       = "${{ values.localstack_endpoint }}"
}

output "test_commands" {
  description = "Comandos para testar a instância"
  value = {
    describe_instances = "aws --endpoint-url=${{ values.localstack_endpoint }} ec2 describe-instances"
    describe_security_groups = "aws --endpoint-url=${{ values.localstack_endpoint }} ec2 describe-security-groups"
    describe_key_pairs = "aws --endpoint-url=${{ values.localstack_endpoint }} ec2 describe-key-pairs"
  }
}

output "web_endpoints" {
  description = "Endpoints web da instância"
  value = {
    web_interface = "http://${aws_instance.main.public_ip}/"
    api_endpoint = "http://${aws_instance.main.public_ip}/api.json"
    health_check = "http://${aws_instance.main.public_ip}/health.json"
    custom_port = "http://${aws_instance.main.public_ip}:8080/"
  }
}

output "ssh_command" {
  description = "Comando SSH para conectar (chave fictícia no LocalStack)"
  value       = "ssh -i ${aws_key_pair.main.key_name}.pem ec2-user@${aws_instance.main.public_ip}"
}

output "instance_info" {
  description = "Informações completas da instância"
  value = {
    name = "${{ values.name }}"
    description = "${{ values.description }}"
    instance_type = "${{ values.instance_type }}"
    ami_id = "${{ values.ami_id }}"
    public_ip_enabled = "${{ values.enable_public_ip }}"
    region = "${{ values.aws_region }}"
  }
}
