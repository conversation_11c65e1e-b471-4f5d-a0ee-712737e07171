#!/bin/bash

# User Data Script for LocalStack EC2 Instance
# Instance: ${instance_name}
# Description: ${description}

# Log all output
exec > >(tee /var/log/user-data.log|logger -t user-data -s 2>/dev/console) 2>&1

echo "=== Starting User Data Script ==="
echo "Instance: ${instance_name}"
echo "Description: ${description}"
echo "Started at: $(date)"

# Update system
echo "Updating system packages..."
yum update -y

# Install basic tools
echo "Installing basic tools..."
yum install -y wget curl unzip git htop tree

# Install Docker
echo "Installing Docker..."
yum install -y docker
systemctl start docker
systemctl enable docker
usermod -a -G docker ec2-user

# Install AWS CLI v2
echo "Installing AWS CLI v2..."
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
./aws/install
rm -rf awscliv2.zip aws/

# Install Terraform
echo "Installing Terraform..."
wget https://releases.hashicorp.com/terraform/1.6.0/terraform_1.6.0_linux_amd64.zip
unzip terraform_1.6.0_linux_amd64.zip
mv terraform /usr/local/bin/
rm terraform_1.6.0_linux_amd64.zip

# Install Node.js (for development)
echo "Installing Node.js..."
curl -fsSL https://rpm.nodesource.com/setup_18.x | bash -
yum install -y nodejs

# Create a simple web server for testing
echo "Setting up test web server..."
yum install -y httpd
systemctl start httpd
systemctl enable httpd

# Create index page
cat > /var/www/html/index.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>${instance_name} - LocalStack EC2</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            margin: 40px; 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        .container { 
            max-width: 800px; 
            margin: 0 auto; 
            background: rgba(255,255,255,0.1); 
            padding: 30px; 
            border-radius: 15px; 
            backdrop-filter: blur(10px);
        }
        .header { 
            text-align: center; 
            margin-bottom: 30px; 
        }
        .info { 
            background: rgba(255,255,255,0.1); 
            padding: 20px; 
            border-radius: 10px; 
            margin: 15px 0; 
        }
        .command {
            background: rgba(0,0,0,0.3);
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 ${instance_name}</h1>
            <p>LocalStack EC2 Instance - Development Environment</p>
        </div>
        
        <div class="info">
            <h3>📋 Instance Information</h3>
            <p><strong>Name:</strong> ${instance_name}</p>
            <p><strong>Description:</strong> ${description}</p>
            <p><strong>Environment:</strong> LocalStack Development</p>
            <p><strong>Deployed:</strong> $(date)</p>
            <p><strong>Managed by:</strong> Terraform + Backstage</p>
        </div>
        
        <div class="info">
            <h3>🛠️ Installed Tools</h3>
            <ul>
                <li>Docker</li>
                <li>AWS CLI v2</li>
                <li>Terraform</li>
                <li>Node.js</li>
                <li>Git</li>
                <li>Apache HTTP Server</li>
            </ul>
        </div>
        
        <div class="info">
            <h3>🧪 LocalStack Commands</h3>
            <p>Use these commands to interact with LocalStack:</p>
            <div class="command">aws --endpoint-url=http://localhost:4566 ec2 describe-instances</div>
            <div class="command">aws --endpoint-url=http://localhost:4566 s3 ls</div>
            <div class="command">curl http://localhost:4566/health</div>
        </div>
        
        <div class="info">
            <h3>🔗 Test Endpoints</h3>
            <p><strong>HTTP:</strong> http://[instance-ip]/</p>
            <p><strong>Custom App:</strong> http://[instance-ip]:8080/</p>
            <p><strong>SSH:</strong> ssh -i key.pem ec2-user@[instance-ip]</p>
        </div>
    </div>
</body>
</html>
EOF

# Create a simple API endpoint for testing
echo "Creating test API..."
cat > /var/www/html/api.json << EOF
{
  "instance": "${instance_name}",
  "description": "${description}",
  "status": "running",
  "environment": "localstack",
  "timestamp": "$(date -Iseconds)",
  "tools": [
    "docker",
    "aws-cli",
    "terraform",
    "nodejs",
    "git"
  ],
  "endpoints": {
    "web": "http://[instance-ip]/",
    "api": "http://[instance-ip]/api.json",
    "health": "http://[instance-ip]/health.json"
  }
}
EOF

# Create health endpoint
cat > /var/www/html/health.json << EOF
{
  "status": "healthy",
  "instance": "${instance_name}",
  "uptime": "$(uptime)",
  "timestamp": "$(date -Iseconds)"
}
EOF

# Configure AWS CLI for LocalStack
echo "Configuring AWS CLI for LocalStack..."
mkdir -p /home/<USER>/.aws
cat > /home/<USER>/.aws/config << EOF
[default]
region = us-east-1

[profile localstack]
region = us-east-1
EOF

cat > /home/<USER>/.aws/credentials << EOF
[default]
aws_access_key_id = sample
aws_secret_access_key = sample

[profile localstack]
aws_access_key_id = sample
aws_secret_access_key = sample
EOF

chown -R ec2-user:ec2-user /home/<USER>/.aws

# Create useful aliases
echo "Creating useful aliases..."
cat >> /home/<USER>/.bashrc << EOF

# LocalStack aliases
alias awslocal='aws --endpoint-url=http://localhost:4566'
alias tf='terraform'
alias ll='ls -la'
alias localstack-health='curl http://localhost:4566/health'

# Welcome message
echo "🚀 Welcome to ${instance_name}!"
echo "📋 Description: ${description}"
echo "🧪 LocalStack commands available with 'awslocal' alias"
echo "🌐 Web interface: http://[your-ip]/"
EOF

echo "=== User Data Script Completed ==="
echo "Instance ${instance_name} is ready!"
echo "Completed at: $(date)"
