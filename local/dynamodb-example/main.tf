# Configure the AWS Provider for LocalStack
terraform {
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  required_version = ">= 1.0"
}

provider "aws" {
  region                      = "us-east-1"
  access_key                  = "test"
  secret_key                  = "test"
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    dynamodb = "http://localhost:4566"
  }
}

# DynamoDB Table - Users
resource "aws_dynamodb_table" "users_table" {
  name           = "localstack-users"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "user_id"
  range_key      = "created_at"

  attribute {
    name = "user_id"
    type = "S"
  }

  attribute {
    name = "created_at"
    type = "S"
  }

  attribute {
    name = "email"
    type = "S"
  }

  attribute {
    name = "status"
    type = "S"
  }

  # Global Secondary Index for email lookups
  global_secondary_index {
    name     = "email-index"
    hash_key = "email"
    projection_type = "ALL"
  }

  # Global Secondary Index for status queries
  global_secondary_index {
    name     = "status-index"
    hash_key = "status"
    range_key = "created_at"
    projection_type = "ALL"
  }

  tags = {
    Name        = "LocalStack Users Table"
    Environment = "development"
    ManagedBy   = "terraform"
  }
}

# DynamoDB Table - Products
resource "aws_dynamodb_table" "products_table" {
  name           = "localstack-products"
  billing_mode   = "PROVISIONED"
  read_capacity  = 5
  write_capacity = 5
  hash_key       = "product_id"

  attribute {
    name = "product_id"
    type = "S"
  }

  attribute {
    name = "category"
    type = "S"
  }

  attribute {
    name = "price"
    type = "N"
  }

  # Global Secondary Index for category queries
  global_secondary_index {
    name            = "category-price-index"
    hash_key        = "category"
    range_key       = "price"
    read_capacity   = 5
    write_capacity  = 5
    projection_type = "ALL"
  }

  tags = {
    Name        = "LocalStack Products Table"
    Environment = "development"
    ManagedBy   = "terraform"
  }
}

# DynamoDB Table - Orders (with TTL)
resource "aws_dynamodb_table" "orders_table" {
  name           = "localstack-orders"
  billing_mode   = "PAY_PER_REQUEST"
  hash_key       = "order_id"

  attribute {
    name = "order_id"
    type = "S"
  }

  attribute {
    name = "user_id"
    type = "S"
  }

  attribute {
    name = "order_date"
    type = "S"
  }

  # Global Secondary Index for user orders
  global_secondary_index {
    name     = "user-orders-index"
    hash_key = "user_id"
    range_key = "order_date"
    projection_type = "ALL"
  }

  # TTL configuration
  ttl {
    attribute_name = "expires_at"
    enabled        = true
  }

  tags = {
    Name        = "LocalStack Orders Table"
    Environment = "development"
    ManagedBy   = "terraform"
  }
}

# Sample data for Users table
resource "aws_dynamodb_table_item" "sample_users" {
  count      = 3
  table_name = aws_dynamodb_table.users_table.name
  hash_key   = aws_dynamodb_table.users_table.hash_key
  range_key  = aws_dynamodb_table.users_table.range_key

  item = jsonencode({
    user_id = {
      S = "user-${count.index + 1}"
    }
    created_at = {
      S = "2024-01-${format("%02d", count.index + 1)}T10:00:00Z"
    }
    email = {
      S = "user${count.index + 1}@example.com"
    }
    name = {
      S = "User ${count.index + 1}"
    }
    status = {
      S = count.index == 0 ? "active" : (count.index == 1 ? "inactive" : "pending")
    }
    age = {
      N = tostring(25 + count.index * 5)
    }
    preferences = {
      M = {
        theme = {
          S = count.index % 2 == 0 ? "dark" : "light"
        }
        notifications = {
          BOOL = count.index != 1
        }
      }
    }
    tags = {
      SS = count.index == 0 ? ["premium", "early-adopter"] : (count.index == 1 ? ["basic"] : ["trial", "new"])
    }
  })
}

# Sample data for Products table
resource "aws_dynamodb_table_item" "sample_products" {
  count      = 5
  table_name = aws_dynamodb_table.products_table.name
  hash_key   = aws_dynamodb_table.products_table.hash_key

  item = jsonencode({
    product_id = {
      S = "prod-${count.index + 1}"
    }
    name = {
      S = "Product ${count.index + 1}"
    }
    category = {
      S = count.index < 2 ? "electronics" : (count.index < 4 ? "books" : "clothing")
    }
    price = {
      N = tostring((count.index + 1) * 19.99)
    }
    description = {
      S = "This is a sample product ${count.index + 1} for LocalStack testing"
    }
    in_stock = {
      BOOL = count.index != 2
    }
    stock_quantity = {
      N = tostring((count.index + 1) * 10)
    }
    created_at = {
      S = "2024-01-${format("%02d", count.index + 1)}T12:00:00Z"
    }
  })
}

# Sample data for Orders table
resource "aws_dynamodb_table_item" "sample_orders" {
  count      = 4
  table_name = aws_dynamodb_table.orders_table.name
  hash_key   = aws_dynamodb_table.orders_table.hash_key

  item = jsonencode({
    order_id = {
      S = "order-${count.index + 1}"
    }
    user_id = {
      S = "user-${(count.index % 3) + 1}"
    }
    order_date = {
      S = "2024-01-${format("%02d", count.index + 10)}T14:${format("%02d", count.index * 15)}:00Z"
    }
    total_amount = {
      N = tostring((count.index + 1) * 49.99)
    }
    status = {
      S = count.index == 0 ? "completed" : (count.index == 1 ? "pending" : (count.index == 2 ? "shipped" : "cancelled"))
    }
    items = {
      L = [
        {
          M = {
            product_id = {
              S = "prod-${count.index + 1}"
            }
            quantity = {
              N = tostring(count.index + 1)
            }
            price = {
              N = tostring((count.index + 1) * 19.99)
            }
          }
        }
      ]
    }
    # TTL: expire after 90 days (for demo purposes)
    expires_at = {
      N = tostring(floor((timestamp() + 90 * 24 * 3600) / 1))
    }
  })
}
