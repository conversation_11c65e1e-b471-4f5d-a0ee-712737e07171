# 🗃️ DynamoDB Example - LocalStack

Este exemplo demonstra como criar e gerenciar tabelas DynamoDB com diferentes configurações usando Terraform e LocalStack.

## 📦 O que será criado

### **Tabelas DynamoDB**
- ✅ **Users Table** - PAY_PER_REQUEST, hash+range key, 2 GSIs
- ✅ **Products Table** - PROVISIONED, hash key, 1 GSI
- ✅ **Orders Table** - PAY_PER_REQUEST, TTL enabled, 1 GSI

### **Índices Secundários Globais (GSI)**
- ✅ **email-index** - Busca por email
- ✅ **status-index** - Busca por status + data
- ✅ **category-price-index** - Busca por categoria + preço
- ✅ **user-orders-index** - Busca pedidos por usuário

### **Dados de Exemplo**
- ✅ **3 usuários** com diferentes status
- ✅ **5 produtos** em categorias variadas
- ✅ **4 pedidos** com TTL configurado

## 🚀 Como executar

### **1. Iniciar LocalStack**
```bash
# Via Docker (recomendado)
docker run --rm -it \
  -p 4566:4566 \
  -e DEBUG=1 \
  localstack/localstack
```

### **2. Executar Terraform**
```bash
# Inicializar
terraform init

# Planejar
terraform plan

# Aplicar
terraform apply
```

### **3. Verificar tabelas criadas**
```bash
# Listar tabelas
aws --endpoint-url=http://localhost:4566 dynamodb list-tables

# Ver estrutura da tabela Users
aws --endpoint-url=http://localhost:4566 dynamodb describe-table \
  --table-name localstack-users
```

## 🔍 Operações básicas

### **Scan (listar todos os itens)**
```bash
# Todos os usuários
aws --endpoint-url=http://localhost:4566 dynamodb scan \
  --table-name localstack-users

# Todos os produtos
aws --endpoint-url=http://localhost:4566 dynamodb scan \
  --table-name localstack-products

# Todos os pedidos
aws --endpoint-url=http://localhost:4566 dynamodb scan \
  --table-name localstack-orders
```

### **Get Item (buscar item específico)**
```bash
# Buscar usuário específico
aws --endpoint-url=http://localhost:4566 dynamodb get-item \
  --table-name localstack-users \
  --key '{"user_id":{"S":"user-1"},"created_at":{"S":"2024-01-01T10:00:00Z"}}'

# Buscar produto específico
aws --endpoint-url=http://localhost:4566 dynamodb get-item \
  --table-name localstack-products \
  --key '{"product_id":{"S":"prod-1"}}'
```

### **Query (buscar com condições)**
```bash
# Buscar usuário por email (usando GSI)
aws --endpoint-url=http://localhost:4566 dynamodb query \
  --table-name localstack-users \
  --index-name email-index \
  --key-condition-expression 'email = :email' \
  --expression-attribute-values '{"email":{"S":"<EMAIL>"}}'

# Buscar produtos por categoria
aws --endpoint-url=http://localhost:4566 dynamodb query \
  --table-name localstack-products \
  --index-name category-price-index \
  --key-condition-expression 'category = :cat' \
  --expression-attribute-values '{":cat":{"S":"electronics"}}'

# Buscar pedidos de um usuário
aws --endpoint-url=http://localhost:4566 dynamodb query \
  --table-name localstack-orders \
  --index-name user-orders-index \
  --key-condition-expression 'user_id = :uid' \
  --expression-attribute-values '{":uid":{"S":"user-1"}}'
```

## 🛠️ Operações CRUD

### **Create (Put Item)**
```bash
# Criar novo usuário
aws --endpoint-url=http://localhost:4566 dynamodb put-item \
  --table-name localstack-users \
  --item '{
    "user_id":{"S":"user-new"},
    "created_at":{"S":"2024-01-15T10:00:00Z"},
    "email":{"S":"<EMAIL>"},
    "name":{"S":"New User"},
    "status":{"S":"active"},
    "age":{"N":"30"}
  }'

# Criar novo produto
aws --endpoint-url=http://localhost:4566 dynamodb put-item \
  --table-name localstack-products \
  --item '{
    "product_id":{"S":"prod-new"},
    "name":{"S":"New Product"},
    "category":{"S":"electronics"},
    "price":{"N":"199.99"},
    "description":{"S":"A new product for testing"},
    "in_stock":{"BOOL":true}
  }'
```

### **Update Item**
```bash
# Atualizar status do usuário
aws --endpoint-url=http://localhost:4566 dynamodb update-item \
  --table-name localstack-users \
  --key '{"user_id":{"S":"user-1"},"created_at":{"S":"2024-01-01T10:00:00Z"}}' \
  --update-expression 'SET #status = :status, #age = :age' \
  --expression-attribute-names '{"#status":"status","#age":"age"}' \
  --expression-attribute-values '{":status":{"S":"updated"},":age":{"N":"26"}}'

# Atualizar preço do produto
aws --endpoint-url=http://localhost:4566 dynamodb update-item \
  --table-name localstack-products \
  --key '{"product_id":{"S":"prod-1"}}' \
  --update-expression 'SET price = :price, stock_quantity = stock_quantity + :qty' \
  --expression-attribute-values '{":price":{"N":"29.99"},":qty":{"N":"5"}}'
```

### **Delete Item**
```bash
# Deletar usuário
aws --endpoint-url=http://localhost:4566 dynamodb delete-item \
  --table-name localstack-users \
  --key '{"user_id":{"S":"user-new"},"created_at":{"S":"2024-01-15T10:00:00Z"}}'

# Deletar produto
aws --endpoint-url=http://localhost:4566 dynamodb delete-item \
  --table-name localstack-products \
  --key '{"product_id":{"S":"prod-new"}}'
```

## 🔄 Operações em lote

### **Batch Get Item**
```bash
# Buscar múltiplos usuários
aws --endpoint-url=http://localhost:4566 dynamodb batch-get-item \
  --request-items '{
    "localstack-users": {
      "Keys": [
        {"user_id":{"S":"user-1"},"created_at":{"S":"2024-01-01T10:00:00Z"}},
        {"user_id":{"S":"user-2"},"created_at":{"S":"2024-01-02T10:00:00Z"}}
      ]
    }
  }'

# Buscar múltiplos produtos
aws --endpoint-url=http://localhost:4566 dynamodb batch-get-item \
  --request-items '{
    "localstack-products": {
      "Keys": [
        {"product_id":{"S":"prod-1"}},
        {"product_id":{"S":"prod-2"}}
      ]
    }
  }'
```

### **Batch Write Item**
```bash
# Criar múltiplos produtos
aws --endpoint-url=http://localhost:4566 dynamodb batch-write-item \
  --request-items '{
    "localstack-products": [
      {
        "PutRequest": {
          "Item": {
            "product_id":{"S":"batch-1"},
            "name":{"S":"Batch Product 1"},
            "category":{"S":"test"},
            "price":{"N":"99.99"}
          }
        }
      },
      {
        "PutRequest": {
          "Item": {
            "product_id":{"S":"batch-2"},
            "name":{"S":"Batch Product 2"},
            "category":{"S":"test"},
            "price":{"N":"149.99"}
          }
        }
      }
    ]
  }'
```

## 🔍 Queries avançadas

### **Filtros e projeções**
```bash
# Buscar usuários ativos com projeção
aws --endpoint-url=http://localhost:4566 dynamodb query \
  --table-name localstack-users \
  --index-name status-index \
  --key-condition-expression '#status = :status' \
  --projection-expression 'user_id, #name, email' \
  --expression-attribute-names '{"#status":"status","#name":"name"}' \
  --expression-attribute-values '{":status":{"S":"active"}}'

# Buscar produtos com filtro de preço
aws --endpoint-url=http://localhost:4566 dynamodb scan \
  --table-name localstack-products \
  --filter-expression 'price > :min_price AND price < :max_price' \
  --expression-attribute-values '{":min_price":{"N":"20"},":max_price":{"N":"100"}}'
```

### **Contagem de itens**
```bash
# Contar usuários por status
aws --endpoint-url=http://localhost:4566 dynamodb query \
  --table-name localstack-users \
  --index-name status-index \
  --key-condition-expression '#status = :status' \
  --select COUNT \
  --expression-attribute-names '{"#status":"status"}' \
  --expression-attribute-values '{":status":{"S":"active"}}'
```

## 📊 Monitoramento

### **Informações da tabela**
```bash
# Ver estatísticas da tabela
aws --endpoint-url=http://localhost:4566 dynamodb describe-table \
  --table-name localstack-users \
  --query 'Table.{Name:TableName,Status:TableStatus,ItemCount:ItemCount,SizeBytes:TableSizeBytes}'

# Listar índices
aws --endpoint-url=http://localhost:4566 dynamodb describe-table \
  --table-name localstack-users \
  --query 'Table.GlobalSecondaryIndexes[].{IndexName:IndexName,Status:IndexStatus}'
```

## 🧪 Testes de TTL

```bash
# Ver configuração de TTL
aws --endpoint-url=http://localhost:4566 dynamodb describe-time-to-live \
  --table-name localstack-orders

# Criar item com TTL curto (expira em 1 minuto)
aws --endpoint-url=http://localhost:4566 dynamodb put-item \
  --table-name localstack-orders \
  --item '{
    "order_id":{"S":"ttl-test"},
    "user_id":{"S":"user-1"},
    "order_date":{"S":"2024-01-20T10:00:00Z"},
    "total_amount":{"N":"99.99"},
    "status":{"S":"test"},
    "expires_at":{"N":"'$(date -d '+1 minute' +%s)'"}
  }'
```

## 🧹 Limpeza

```bash
# Destruir recursos
terraform destroy

# Ou deletar tabelas manualmente
aws --endpoint-url=http://localhost:4566 dynamodb delete-table --table-name localstack-users
aws --endpoint-url=http://localhost:4566 dynamodb delete-table --table-name localstack-products
aws --endpoint-url=http://localhost:4566 dynamodb delete-table --table-name localstack-orders
```

## 📚 Recursos relacionados

- **DynamoDB Developer Guide**: https://docs.aws.amazon.com/dynamodb/
- **LocalStack DynamoDB**: https://docs.localstack.cloud/user-guide/aws/dynamodb/
- **Terraform AWS DynamoDB**: https://registry.terraform.io/providers/hashicorp/aws/latest/docs/resources/dynamodb_table

## 🎯 Próximos passos

1. **Implementar** DynamoDB Streams
2. **Configurar** backup automático
3. **Testar** performance com dados maiores
4. **Integrar** com Lambda triggers
