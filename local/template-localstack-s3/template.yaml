apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-localstack-s3
  title: LocalStack S3 Bucket - Terraform
  description: Cria um bucket S3 no LocalStack para desenvolvimento local
  tags:
    - s3
    - localstack
    - development
    - local
spec:
  owner: backstage
  type: aws
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome do Bucket S3
          type: string
          description: Nome do bucket (será adicionado sufixo único)
          pattern: ^[a-z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição do Bucket S3
        owner:
          title: GroupID
          type: string
          description: Responsável pelo Bucket
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
    - title: Configurações do Bucket
      required:
        - bucket_purpose
        - enable_versioning
      properties:
        bucket_purpose:
          title: Propósito do Bucket
          type: string
          description: Finalidade principal do bucket
          default: data-storage
          enum:
            - data-storage
            - static-website
            - backup
            - logs
            - media-assets
          enumNames:
            - Armazenamento de Dados
            - Site Estático
            - Backup
            - Logs
            - Assets de Mídia
        enable_versioning:
          title: Habilitar Versionamento?
          type: boolean
          description: Habilitar versionamento de objetos
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_public_access:
          title: Permitir Acesso Público?
          type: boolean
          description: Permitir acesso público de leitura
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações LocalStack
      properties:
        localstack_endpoint:
          title: Endpoint LocalStack
          type: string
          description: URL do endpoint LocalStack
          default: http://localhost:4566
        localstack_region:
          title: Região LocalStack
          type: string
          description: Região simulada no LocalStack
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
    - title: Bitbucket
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repositório
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - almappro.tractebelenergia.com.br
            allowedOwners:
              - backstage
            allowedProjects:
              - BAC
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          bucket_purpose: ${{ parameters.bucket_purpose }}
          enable_versioning: ${{ parameters.enable_versioning }}
          enable_public_access: ${{ parameters.enable_public_access }}
          localstack_endpoint: ${{ parameters.localstack_endpoint }}
          localstack_region: ${{ parameters.localstack_region }}
    - id: publish
      name: Publish
      action: publish:bitbucketServer
      input:
        allowedHosts:
          - almappro.tractebelenergia.com.br
        description: Componente ${{ parameters.name }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
