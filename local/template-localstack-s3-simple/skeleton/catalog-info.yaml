apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: localstack-s3-${{values.name}}
  description: LocalStack S3 Bucket - ${{values.description}}
  annotations:
    github.com/project-slug: ${{values.destination.owner + "/" + values.destination.repo}}
    backstage.io/techdocs-ref: dir:.
  tags:
    - localstack
    - s3
    - development
spec:
  type: service
  lifecycle: experimental
  owner: ${{values.owner}}
