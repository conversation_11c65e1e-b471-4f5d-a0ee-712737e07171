terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

provider "aws" {
  access_key = "sample"
  secret_key = "sample"
  region     = "us-east-1"

  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    s3 = "http://localhost:4566"
  }
}

# Random suffix for bucket name uniqueness
resource "random_string" "bucket_suffix" {
  length  = 8
  special = false
  upper   = false
}

# S3 Bucket
resource "aws_s3_bucket" "main" {
  bucket = "${{values.name}}-${random_string.bucket_suffix.result}"

  tags = {
    Name        = "${{values.name}}-${random_string.bucket_suffix.result}"
    Environment = "development"
    ManagedBy   = "terraform"
    LocalStack  = "true"
  }
}

# Sample object for testing
resource "aws_s3_object" "sample" {
  bucket  = aws_s3_bucket.main.id
  key     = "sample.txt"
  content = "Hello from LocalStack S3! Bucket: ${aws_s3_bucket.main.bucket}"
}

# Outputs
output "bucket_name" {
  description = "Name of the S3 bucket"
  value       = aws_s3_bucket.main.bucket
}

output "bucket_arn" {
  description = "ARN of the S3 bucket"
  value       = aws_s3_bucket.main.arn
}

output "sample_object_url" {
  description = "URL to access the sample object"
  value       = "http://localhost:4566/${aws_s3_bucket.main.bucket}/sample.txt"
}

output "aws_cli_test_commands" {
  description = "Commands to test the bucket"
  value = {
    list_buckets = "aws --endpoint-url=http://localhost:4566 s3 ls"
    list_objects = "aws --endpoint-url=http://localhost:4566 s3 ls s3://${aws_s3_bucket.main.bucket}/"
    download_sample = "aws --endpoint-url=http://localhost:4566 s3 cp s3://${aws_s3_bucket.main.bucket}/sample.txt ."
  }
}
