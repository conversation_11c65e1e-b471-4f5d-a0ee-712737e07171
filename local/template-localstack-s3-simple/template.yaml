apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-localstack-s3-simple
  title: LocalStack S3 Simple - Terraform
  description: Cria um bucket S3 simples no LocalStack para teste
  tags:
    - s3
    - localstack
    - development
    - simple
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
      properties:
        name:
          title: Nome do Bucket S3
          type: string
          description: Nome do bucket (será adicionado sufixo único)
          pattern: ^[a-z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição do Bucket S3
        owner:
          title: Owner
          type: string
          description: Owner of the component
          default: user:guest
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
