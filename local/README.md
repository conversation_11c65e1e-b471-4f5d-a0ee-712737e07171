# 🧪 Templates LocalStack - Desenvolvimento Local

Este diretório contém templates do Backstage para desenvolvimento local usando LocalStack, seguindo o padrão Engie.

## 🎯 O que é LocalStack?

LocalStack é uma plataforma que simula serviços AWS localmente, permitindo desenvolvimento e testes sem custos da AWS.

### **Benefícios**
- ✅ **Desenvolvimento offline** - Sem necessidade de internet
- ✅ **Sem custos AWS** - Testes gratuitos
- ✅ **Ambiente isolado** - Não afeta recursos reais
- ✅ **Desenvolvimento rápido** - Iterações mais rápidas

## 📦 Templates Disponíveis

### 🗄️ **S3 Bucket** - `template-localstack-s3`
- **Descrição**: Bucket S3 para desenvolvimento local
- **Recursos**: S3 Bucket, versionamento, políticas, objeto de exemplo
- **Uso**: Armazenamento de arquivos, sites estáticos

### 🖥️ **EC2 Instance** - `template-localstack-ec2`
- **Descrição**: Instância EC2 simulada localmente
- **Recursos**: EC2, Security Group, Key Pair, User Data
- **Uso**: Testes de infraestrutura, aplicações

### 🗃️ **DynamoDB Table** - `template-localstack-dynamodb`
- **Descrição**: Tabela DynamoDB para desenvolvimento
- **Recursos**: Tabela, itens de exemplo, configurações
- **Uso**: Banco NoSQL, testes de aplicação

## 🚀 Como Usar

### **Pré-requisitos**
```bash
# Instalar Docker
docker --version

# Instalar LocalStack
pip install localstack
# ou
brew install localstack/tap/localstack-cli
```

### **1. Iniciar LocalStack**
```bash
# Método 1: Docker direto
docker run --rm -it -p 4566:4566 localstack/localstack

# Método 2: LocalStack CLI
localstack start

# Método 3: Docker Compose (recomendado)
# Criar docker-compose.yml:
version: '3.8'
services:
  localstack:
    image: localstack/localstack:latest
    ports:
      - "4566:4566"
    environment:
      - SERVICES=s3,ec2,dynamodb,iam
      - DEBUG=1
      - DATA_DIR=/tmp/localstack/data
    volumes:
      - "/tmp/localstack:/tmp/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"

# Executar:
docker-compose up -d
```

### **2. Verificar LocalStack**
```bash
# Verificar se está rodando
curl http://localhost:4566/health

# Listar serviços disponíveis
curl http://localhost:4566/_localstack/health
```

### **3. Usar Templates no Backstage**
1. Acesse o Backstage
2. Vá para **Create** → **Choose a template**
3. Procure por templates com prefixo `template-localstack-`
4. Preencha os parâmetros (endpoint padrão: `http://localhost:4566`)
5. Crie o repositório

### **4. Aplicar Terraform**
```bash
# No repositório criado
terraform init
terraform plan
terraform apply

# Verificar recursos criados
aws --endpoint-url=http://localhost:4566 s3 ls
aws --endpoint-url=http://localhost:4566 ec2 describe-instances
aws --endpoint-url=http://localhost:4566 dynamodb list-tables
```

## 🔧 Configuração LocalStack

### **Provider Terraform**
```hcl
provider "aws" {
  access_key = "sample"
  secret_key = "sample"
  region     = "us-east-1"

  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    s3       = "http://localhost:4566"
    ec2      = "http://localhost:4566"
    dynamodb = "http://localhost:4566"
  }
}
```

### **AWS CLI**
```bash
# Configurar perfil LocalStack
aws configure set aws_access_key_id sample --profile localstack
aws configure set aws_secret_access_key sample --profile localstack
aws configure set region us-east-1 --profile localstack

# Usar com endpoint
aws --profile localstack --endpoint-url=http://localhost:4566 s3 ls
```

## 🧪 Testes e Validação

### **S3 Bucket**
```bash
# Listar buckets
aws --endpoint-url=http://localhost:4566 s3 ls

# Fazer upload de arquivo
echo "Hello LocalStack" > test.txt
aws --endpoint-url=http://localhost:4566 s3 cp test.txt s3://[bucket-name]/

# Baixar arquivo
aws --endpoint-url=http://localhost:4566 s3 cp s3://[bucket-name]/test.txt downloaded.txt
```

### **EC2 Instance**
```bash
# Listar instâncias
aws --endpoint-url=http://localhost:4566 ec2 describe-instances

# Verificar security groups
aws --endpoint-url=http://localhost:4566 ec2 describe-security-groups

# Verificar key pairs
aws --endpoint-url=http://localhost:4566 ec2 describe-key-pairs
```

### **DynamoDB Table**
```bash
# Listar tabelas
aws --endpoint-url=http://localhost:4566 dynamodb list-tables

# Descrever tabela
aws --endpoint-url=http://localhost:4566 dynamodb describe-table --table-name [table-name]

# Scan tabela
aws --endpoint-url=http://localhost:4566 dynamodb scan --table-name [table-name]

# Inserir item
aws --endpoint-url=http://localhost:4566 dynamodb put-item \
  --table-name [table-name] \
  --item '{"id":{"S":"test-id"},"data":{"S":"test data"}}'
```

## 🔍 Troubleshooting

### **LocalStack não inicia**
```bash
# Verificar portas
lsof -i :4566

# Limpar containers
docker stop $(docker ps -q --filter ancestor=localstack/localstack)
docker rm $(docker ps -aq --filter ancestor=localstack/localstack)

# Reiniciar
docker run --rm -it -p 4566:4566 localstack/localstack
```

### **Terraform falha**
```bash
# Verificar conectividade
curl http://localhost:4566/health

# Verificar logs LocalStack
docker logs [container-id]

# Validar configuração
terraform validate
```

### **AWS CLI não funciona**
```bash
# Verificar configuração
aws configure list --profile localstack

# Testar conectividade
aws --endpoint-url=http://localhost:4566 sts get-caller-identity
```

## 📚 Recursos Úteis

### **Documentação**
- [LocalStack Docs](https://docs.localstack.cloud/)
- [Terraform AWS Provider](https://registry.terraform.io/providers/hashicorp/aws)
- [AWS CLI Reference](https://docs.aws.amazon.com/cli/)

### **Ferramentas**
- **LocalStack Web UI**: `http://localhost:4566/_localstack/cockpit`
- **Health Check**: `http://localhost:4566/health`
- **Service Status**: `http://localhost:4566/_localstack/health`

### **Serviços Suportados (Free Tier)**
- ✅ S3, DynamoDB, Lambda, SQS, SNS
- ✅ CloudFormation, CloudWatch, IAM
- ✅ API Gateway, Route53, SES
- ✅ EC2 (limitado), RDS (limitado)

## 🎯 Próximos Passos

1. **Testar templates** criados
2. **Expandir para outros serviços** (Lambda, SQS, SNS)
3. **Integrar com CI/CD** para testes automatizados
4. **Criar scripts** de setup automatizado

---

**Criado para**: Desenvolvimento Local com LocalStack  
**Padrão**: Engie  
**Última atualização**: $(date)
