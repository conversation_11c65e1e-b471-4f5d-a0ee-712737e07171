apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-localstack-dynamodb
  title: LocalStack DynamoDB Table - Terraform
  description: Cria uma tabela DynamoDB no LocalStack para desenvolvimento local
  tags:
    - dynamodb
    - localstack
    - development
    - local
    - database
spec:
  owner: user:guest
  type: service
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome da Tabela DynamoDB
          type: string
          description: Nome da tabela
          pattern: ^[a-zA-Z0-9\-_]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição da tabela DynamoDB
        owner:
          title: Owner
          type: string
          description: Owner of the component
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
              - User
    - title: Configurações da Tabela
      required:
        - hash_key
        - billing_mode
      properties:
        hash_key:
          title: Chave <PERSON> (Partition Key)
          type: string
          description: Nome do atributo da chave de partição
          default: id
        range_key:
          title: Chave Range (Sort Key)
          type: string
          description: Nome do atributo da chave de ordenação (opcional)
        billing_mode:
          title: Modo de Cobrança
          type: string
          description: Modo de cobrança da tabela
          default: PAY_PER_REQUEST
          enum:
            - PAY_PER_REQUEST
            - PROVISIONED
          enumNames:
            - Pay Per Request (On-Demand)
            - Provisioned Throughput
        enable_point_in_time_recovery:
          title: Habilitar Point-in-Time Recovery?
          type: boolean
          description: Habilitar recuperação point-in-time
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_server_side_encryption:
          title: Habilitar Criptografia?
          type: boolean
          description: Habilitar criptografia server-side
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
      dependencies:
        billing_mode:
          oneOf:
            - properties:
                billing_mode:
                  enum:
                    - PROVISIONED
                read_capacity:
                  title: Capacidade de Leitura
                  type: integer
                  description: Unidades de capacidade de leitura
                  default: 5
                  minimum: 1
                  maximum: 1000
                write_capacity:
                  title: Capacidade de Escrita
                  type: integer
                  description: Unidades de capacidade de escrita
                  default: 5
                  minimum: 1
                  maximum: 1000
            - properties:
                billing_mode:
                  enum:
                    - PAY_PER_REQUEST
    - title: Configurações LocalStack
      properties:
        localstack_endpoint:
          title: Endpoint LocalStack
          type: string
          description: URL do endpoint LocalStack
          default: http://localhost:4566
        localstack_region:
          title: Região LocalStack
          type: string
          description: Região simulada no LocalStack
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
    - title: Repository Configuration
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repository Location
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - github.com
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          hash_key: ${{ parameters.hash_key }}
          range_key: ${{ parameters.range_key }}
          billing_mode: ${{ parameters.billing_mode }}
          read_capacity: ${{ parameters.read_capacity }}
          write_capacity: ${{ parameters.write_capacity }}
          enable_point_in_time_recovery: ${{ parameters.enable_point_in_time_recovery }}
          enable_server_side_encryption: ${{ parameters.enable_server_side_encryption }}
          localstack_endpoint: ${{ parameters.localstack_endpoint }}
          localstack_region: ${{ parameters.localstack_region }}
    - id: publish
      name: Publish
      action: publish:github
      input:
        allowedHosts: ['github.com']
        description: ${{ parameters.description }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
