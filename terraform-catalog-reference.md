# 📚 Catálogo de Referência: Templates Terraform para AWS

## 🎯 Visão Geral

Este catálogo contém templates de referência para os recursos AWS mais utilizados, organizados por categoria. Cada template segue o padrão estabelecido e pode ser usado como base para criar novos recursos.

## 📋 Índice por Categoria

### 🖥️ Compute

- [EC2 Instance](#ec2-instance)
- [Auto Scaling Group](#auto-scaling-group)
- [Lambda Function](#lambda-function)
- [ECS Service](#ecs-service)
- [EKS Cluster](#eks-cluster)

### 🗄️ Storage

- [S3 Bucket](#s3-bucket)
- [EBS Volume](#ebs-volume)
- [EFS File System](#efs-file-system)

### 🗃️ Database

- [RDS Instance](#rds-instance)
- [DynamoDB Table](#dynamodb-table)
- [ElastiCache Cluster](#elasticache-cluster)

### 🌐 Networking

- [VPC](#vpc)
- [Security Group](#security-group)
- [Load Balancer](#load-balancer)
- [CloudFront Distribution](#cloudfront-distribution)

### 🔧 Management & Monitoring

- [CloudWatch Dashboard](#cloudwatch-dashboard)
- [SNS Topic](#sns-topic)
- [SQS Queue](#sqs-queue)

---

## 🖥️ COMPUTE

### EC2 Instance

**Arquivo**: `examples/ec2-instance/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - instanceType: [t3.micro, t3.small, t3.medium, t3.large, m5.large]
  - region: [us-east-1, us-west-2, eu-west-1]
  - keyPairName: string
  - enablePublicIP: boolean
  - volumeSize: integer (8-100 GB)

# Recursos criados
resources:
  - aws_instance
  - aws_security_group
  - aws_eip (opcional)
  - user_data script
```

### Auto Scaling Group

**Arquivo**: `examples/auto-scaling-group/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - minSize: integer (1-10)
  - maxSize: integer (1-20)
  - desiredCapacity: integer (1-10)
  - instanceType: [t3.micro, t3.small, t3.medium]
  - healthCheckType: [EC2, ELB]

# Recursos criados
resources:
  - aws_launch_template
  - aws_autoscaling_group
  - aws_autoscaling_policy
  - aws_cloudwatch_metric_alarm
```

### Lambda Function

**Arquivo**: `examples/lambda-function/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - runtime: [python3.9, nodejs18.x, java11, go1.x]
  - memorySize: integer (128-3008 MB)
  - timeout: integer (3-900 seconds)
  - environmentVariables: object

# Recursos criados
resources:
  - aws_lambda_function
  - aws_iam_role
  - aws_iam_role_policy_attachment
  - aws_cloudwatch_log_group
```

### ECS Service

**Arquivo**: `examples/ecs-service/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - clusterName: string
  - serviceName: string
  - taskDefinitionFamily: string
  - desiredCount: integer (1-10)
  - containerImage: string

# Recursos criados
resources:
  - aws_ecs_cluster
  - aws_ecs_task_definition
  - aws_ecs_service
  - aws_iam_role (task execution)
```

### EKS Cluster

**Arquivo**: `examples/eks-cluster/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - clusterName: string
  - kubernetesVersion: [1.24, 1.25, 1.26]
  - nodeGroupInstanceType: [t3.medium, t3.large, m5.large]
  - nodeGroupDesiredSize: integer (1-5)

# Recursos criados
resources:
  - aws_eks_cluster
  - aws_eks_node_group
  - aws_iam_role (cluster + node group)
  - aws_vpc (opcional)
```

---

## 🗄️ STORAGE

### S3 Bucket

**Arquivo**: `examples/s3-bucket/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - bucketPurpose: [static-website, data-storage, backup, logs, media-assets]
  - enableVersioning: boolean
  - enableEncryption: boolean
  - enablePublicAccess: boolean
  - lifecycleEnabled: boolean

# Recursos criados
resources:
  - aws_s3_bucket
  - aws_s3_bucket_versioning
  - aws_s3_bucket_server_side_encryption_configuration
  - aws_s3_bucket_public_access_block
  - aws_s3_bucket_lifecycle_configuration
```

### EBS Volume

**Arquivo**: `examples/ebs-volume/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - volumeType: [gp3, gp2, io1, io2]
  - volumeSize: integer (1-16384 GB)
  - iops: integer (100-64000)
  - encrypted: boolean
  - attachToInstance: string (instance ID)

# Recursos criados
resources:
  - aws_ebs_volume
  - aws_volume_attachment (opcional)
  - aws_ebs_snapshot (opcional)
```

### EFS File System

**Arquivo**: `examples/efs-filesystem/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - performanceMode: [generalPurpose, maxIO]
  - throughputMode: [provisioned, bursting]
  - encrypted: boolean
  - backupPolicy: [ENABLED, DISABLED]

# Recursos criados
resources:
  - aws_efs_file_system
  - aws_efs_mount_target
  - aws_efs_backup_policy
  - aws_security_group (NFS)
```

---

## 🗃️ DATABASE

### RDS Instance

**Arquivo**: `examples/rds-database/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - engine: [mysql, postgres, mariadb, oracle-ee, sqlserver-ex]
  - instanceClass: [db.t3.micro, db.t3.small, db.r5.large]
  - allocatedStorage: integer (20-1000 GB)
  - multiAZ: boolean
  - enableBackups: boolean
  - enableEncryption: boolean

# Recursos criados
resources:
  - aws_db_instance
  - aws_db_subnet_group
  - aws_security_group
  - aws_db_parameter_group (opcional)
```

### DynamoDB Table

**Arquivo**: `examples/dynamodb-table/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - tableName: string
  - hashKey: string
  - rangeKey: string (opcional)
  - billingMode: [PAY_PER_REQUEST, PROVISIONED]
  - readCapacity: integer (1-40000)
  - writeCapacity: integer (1-40000)

# Recursos criados
resources:
  - aws_dynamodb_table
  - aws_dynamodb_table_item (opcional)
  - aws_appautoscaling_target (opcional)
  - aws_appautoscaling_policy (opcional)
```

### ElastiCache Cluster

**Arquivo**: `examples/elasticache-cluster/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - engine: [redis, memcached]
  - nodeType: [cache.t3.micro, cache.t3.small, cache.r6g.large]
  - numCacheNodes: integer (1-20)
  - parameterGroupName: string
  - subnetGroupName: string

# Recursos criados
resources:
  - aws_elasticache_cluster
  - aws_elasticache_subnet_group
  - aws_elasticache_parameter_group
  - aws_security_group
```

---

## 🌐 NETWORKING

### VPC

**Arquivo**: `examples/vpc/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - vpcCidr: string (10.0.0.0/16)
  - enableDnsHostnames: boolean
  - enableDnsSupport: boolean
  - publicSubnetCidrs: array
  - privateSubnetCidrs: array
  - availabilityZones: array

# Recursos criados
resources:
  - aws_vpc
  - aws_subnet (public/private)
  - aws_internet_gateway
  - aws_nat_gateway
  - aws_route_table
  - aws_route_table_association
```

### Security Group

**Arquivo**: `examples/security-group/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - sgType: [web-server, database, load-balancer, application-server, custom]
  - allowSSH: boolean
  - allowHTTP: boolean
  - allowHTTPS: boolean
  - customPorts: string (comma-separated)

# Recursos criados
resources:
  - aws_security_group
  - aws_security_group_rule (ingress/egress)
```

### Load Balancer

**Arquivo**: `examples/load-balancer/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - loadBalancerType: [application, network, gateway]
  - scheme: [internet-facing, internal]
  - ipAddressType: [ipv4, dualstack]
  - enableDeletionProtection: boolean
  - targetType: [instance, ip, lambda]

# Recursos criados
resources:
  - aws_lb
  - aws_lb_target_group
  - aws_lb_listener
  - aws_lb_listener_rule
  - aws_security_group
```

### CloudFront Distribution

**Arquivo**: `examples/cloudfront-distribution/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - originDomainName: string
  - originPath: string
  - priceClass: [PriceClass_All, PriceClass_100, PriceClass_200]
  - enableCompression: boolean
  - enableLogging: boolean

# Recursos criados
resources:
  - aws_cloudfront_distribution
  - aws_cloudfront_origin_access_identity
  - aws_s3_bucket_policy (se origem S3)
```

---

## 🔧 MANAGEMENT & MONITORING

### CloudWatch Dashboard

**Arquivo**: `examples/cloudwatch-dashboard/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - dashboardName: string
  - widgets: array
  - period: integer (60-86400 seconds)
  - region: string

# Recursos criados
resources:
  - aws_cloudwatch_dashboard
  - aws_cloudwatch_metric_alarm
  - aws_cloudwatch_log_group
```

### SNS Topic

**Arquivo**: `examples/sns-topic/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - topicName: string
  - displayName: string
  - deliveryPolicy: object
  - subscriptionProtocol: [email, sms, sqs, lambda, http, https]
  - subscriptionEndpoint: string

# Recursos criados
resources:
  - aws_sns_topic
  - aws_sns_topic_subscription
  - aws_sns_topic_policy
```

### SQS Queue

**Arquivo**: `examples/sqs-queue/template.yaml`

```yaml
# Parâmetros principais
parameters:
  - queueName: string
  - fifoQueue: boolean
  - visibilityTimeoutSeconds: integer (0-43200)
  - messageRetentionSeconds: integer (60-1209600)
  - maxReceiveCount: integer (1-1000)
  - enableDeadLetterQueue: boolean

# Recursos criados
resources:
  - aws_sqs_queue
  - aws_sqs_queue (dead letter)
  - aws_sqs_queue_policy
```

---

## 🛠️ Como Usar Este Catálogo

### 1. Escolher Template Base

Identifique o recurso AWS que você precisa e use o template correspondente como base.

### 2. Copiar Estrutura

```bash
cp -r examples/template-base examples/meu-novo-recurso
```

### 3. Personalizar Parâmetros

Edite o `template.yaml` para incluir os parâmetros específicos do seu caso de uso.

### 4. Adaptar Terraform

Modifique os arquivos `.tf` para implementar a lógica específica do recurso.

### 5. Testar e Validar

```bash
# Validar sintaxe Terraform
terraform validate

# Planejar mudanças
terraform plan

# Aplicar (em ambiente de teste)
terraform apply
```

## 📋 Checklist de Criação

- [ ] Template.yaml com parâmetros corretos
- [ ] main.tf com recursos principais
- [ ] variables.tf com todas as variáveis
- [ ] outputs.tf com informações úteis
- [ ] catalog-info.yaml com metadados
- [ ] Configuração adicionada ao app-config.yaml
- [ ] Testes realizados em ambiente de desenvolvimento
- [ ] Documentação atualizada

## 🎯 Próximos Recursos a Implementar

### Alta Prioridade

- [ ] API Gateway
- [ ] Route 53 (DNS)
- [ ] IAM Roles e Policies
- [ ] Secrets Manager
- [ ] Parameter Store

### Média Prioridade

- [ ] CodePipeline
- [ ] CodeBuild
- [ ] ECR Repository
- [ ] Kinesis Stream
- [ ] Step Functions

### Baixa Prioridade

- [ ] Glue Job
- [ ] Athena Workgroup
- [ ] EMR Cluster
- [ ] Redshift Cluster
- [ ] ElasticSearch Domain

---

**Última atualização**: $(date)
**Versão**: 1.0
**Mantido por**: DevOps Team
