#!/bin/bash

# Script de teste rápido para LocalStack com credenciais sample/sample

echo "🧪 Teste Rápido LocalStack - Credenciais sample/sample"
echo "=================================================="

# Cores para output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Função para verificar comando
check_command() {
    if command -v $1 &> /dev/null; then
        echo -e "${GREEN}✅ $1 está instalado${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 não está instalado${NC}"
        return 1
    fi
}

# Função para testar endpoint
test_endpoint() {
    if curl -s $1 > /dev/null; then
        echo -e "${GREEN}✅ $1 está respondendo${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 não está respondendo${NC}"
        return 1
    fi
}

echo -e "${BLUE}1. Verificando dependências...${NC}"
check_command "docker"
check_command "aws"
check_command "curl"

echo -e "\n${BLUE}2. Verificando LocalStack...${NC}"
if test_endpoint "http://localhost:4566/health"; then
    echo -e "${GREEN}LocalStack está rodando!${NC}"
else
    echo -e "${YELLOW}LocalStack não está rodando. Iniciando...${NC}"
    echo "docker run --rm -it -p 4566:4566 localstack/localstack"
    echo -e "${YELLOW}Execute o comando acima em outro terminal e rode este script novamente.${NC}"
    exit 1
fi

echo -e "\n${BLUE}3. Configurando AWS CLI...${NC}"
# Configurar perfil LocalStack com credenciais sample/sample
aws configure set aws_access_key_id sample --profile localstack
aws configure set aws_secret_access_key sample --profile localstack
aws configure set region us-east-1 --profile localstack

echo -e "${GREEN}✅ Perfil 'localstack' configurado com credenciais sample/sample${NC}"

# Criar alias temporário
alias awslocal='aws --endpoint-url=http://localhost:4566 --profile localstack'

echo -e "\n${BLUE}4. Testando conectividade...${NC}"
if aws --endpoint-url=http://localhost:4566 --profile localstack sts get-caller-identity &> /dev/null; then
    echo -e "${GREEN}✅ AWS CLI conectado ao LocalStack${NC}"
else
    echo -e "${RED}❌ Erro na conexão AWS CLI${NC}"
    exit 1
fi

echo -e "\n${BLUE}5. Testando S3...${NC}"
# Criar bucket teste
BUCKET_NAME="teste-bucket-$(date +%s)"
if aws --endpoint-url=http://localhost:4566 --profile localstack s3 mb s3://$BUCKET_NAME &> /dev/null; then
    echo -e "${GREEN}✅ Bucket criado: $BUCKET_NAME${NC}"
    
    # Upload arquivo
    echo "Hello LocalStack with sample credentials!" > test-file.txt
    if aws --endpoint-url=http://localhost:4566 --profile localstack s3 cp test-file.txt s3://$BUCKET_NAME/ &> /dev/null; then
        echo -e "${GREEN}✅ Arquivo enviado para S3${NC}"
        
        # Download arquivo
        if aws --endpoint-url=http://localhost:4566 --profile localstack s3 cp s3://$BUCKET_NAME/test-file.txt downloaded-file.txt &> /dev/null; then
            echo -e "${GREEN}✅ Arquivo baixado do S3${NC}"
            rm -f test-file.txt downloaded-file.txt
        fi
    fi
    
    # Limpar bucket
    aws --endpoint-url=http://localhost:4566 --profile localstack s3 rb s3://$BUCKET_NAME --force &> /dev/null
    echo -e "${GREEN}✅ Bucket removido${NC}"
else
    echo -e "${RED}❌ Erro ao criar bucket S3${NC}"
fi

echo -e "\n${BLUE}6. Testando DynamoDB...${NC}"
# Criar tabela teste
TABLE_NAME="TestTable$(date +%s)"
if aws --endpoint-url=http://localhost:4566 --profile localstack dynamodb create-table \
    --table-name $TABLE_NAME \
    --attribute-definitions AttributeName=id,AttributeType=S \
    --key-schema AttributeName=id,KeyType=HASH \
    --billing-mode PAY_PER_REQUEST &> /dev/null; then
    echo -e "${GREEN}✅ Tabela DynamoDB criada: $TABLE_NAME${NC}"
    
    # Inserir item
    if aws --endpoint-url=http://localhost:4566 --profile localstack dynamodb put-item \
        --table-name $TABLE_NAME \
        --item '{"id":{"S":"test-id"},"name":{"S":"Test Item"},"credentials":{"S":"sample/sample"}}' &> /dev/null; then
        echo -e "${GREEN}✅ Item inserido na tabela${NC}"
        
        # Buscar item
        if aws --endpoint-url=http://localhost:4566 --profile localstack dynamodb get-item \
            --table-name $TABLE_NAME \
            --key '{"id":{"S":"test-id"}}' &> /dev/null; then
            echo -e "${GREEN}✅ Item recuperado da tabela${NC}"
        fi
    fi
    
    # Limpar tabela
    aws --endpoint-url=http://localhost:4566 --profile localstack dynamodb delete-table --table-name $TABLE_NAME &> /dev/null
    echo -e "${GREEN}✅ Tabela removida${NC}"
else
    echo -e "${RED}❌ Erro ao criar tabela DynamoDB${NC}"
fi

echo -e "\n${BLUE}7. Testando EC2...${NC}"
# Listar instâncias (pode estar vazio)
if aws --endpoint-url=http://localhost:4566 --profile localstack ec2 describe-instances &> /dev/null; then
    echo -e "${GREEN}✅ Comando EC2 funcionando${NC}"
    
    # Listar VPCs
    if aws --endpoint-url=http://localhost:4566 --profile localstack ec2 describe-vpcs &> /dev/null; then
        echo -e "${GREEN}✅ VPCs listadas (LocalStack cria VPC padrão)${NC}"
    fi
else
    echo -e "${RED}❌ Erro ao acessar EC2${NC}"
fi

echo -e "\n${GREEN}🎉 Teste Completo!${NC}"
echo "=================================================="
echo -e "${BLUE}📋 Resumo da Configuração:${NC}"
echo "• Credenciais: sample/sample"
echo "• Região: us-east-1"
echo "• Endpoint: http://localhost:4566"
echo "• Perfil: localstack"
echo ""
echo -e "${BLUE}💡 Comandos úteis:${NC}"
echo "# Alias recomendado:"
echo "alias awslocal='aws --endpoint-url=http://localhost:4566 --profile localstack'"
echo ""
echo "# Comandos de teste:"
echo "awslocal s3 ls"
echo "awslocal ec2 describe-instances"
echo "awslocal dynamodb list-tables"
echo "awslocal sts get-caller-identity"
echo ""
echo -e "${BLUE}🔗 URLs úteis:${NC}"
echo "• Health: http://localhost:4566/health"
echo "• Dashboard: http://localhost:4566/_localstack/cockpit"
echo "• Service Status: http://localhost:4566/_localstack/health"
echo ""
echo -e "${GREEN}✅ LocalStack está pronto para uso com credenciais sample/sample!${NC}"
