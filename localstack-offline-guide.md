# 🧪 Guia LocalStack Offline - Sem GitHub

## 🎯 **SOLUÇÃO: Templates LocalStack Locais**

Agora você pode testar LocalStack **sem precisar de repositório GitHub**! Os templates geram os arquivos Terraform diretamente na sua máquina.

## 📦 **Templates Offline Criados**

### ✅ **Templates Loca<PERSON> (Sem GitHub)**
1. **`template-localstack-s3-local`** - Bucket S3 offline
2. **`template-localstack-ec2-local`** - Instância EC2 offline  
3. **`template-localstack-dynamodb-local`** - Tabela DynamoDB offline

### 🔧 **Como Funcionam**
- ✅ **Geram arquivos** diretamente no diretório local
- ✅ **Sem repositório** necessário
- ✅ **Prontos para usar** com `terraform apply`
- ✅ **Dados de exemplo** incluídos
- ✅ **Documentação completa** gerada

## 🚀 **Como Usar**

### **1. Iniciar LocalStack**
```bash
# Terminal 1
docker run --rm -it -p 4566:4566 localstack/localstack

# Verificar se está funcionando
curl http://localhost:4566/health
```

### **2. Iniciar Backstage**
```bash
# Terminal 2
export NODE_OPTIONS="--no-node-snapshot"
yarn dev

# Ou usar o script
./start-backstage.sh
```

### **3. Usar Templates Offline**
1. **Acesse**: http://localhost:3000
2. **Create** → **Choose a template**
3. **Procure**: Templates com "Local" no nome:
   - `LocalStack S3 Local`
   - `LocalStack EC2 Local`
   - `LocalStack DynamoDB Local`
4. **Preencha**: Apenas nome, descrição e configurações
5. **Crie**: Sem repositório!

### **4. Aplicar Terraform**
```bash
# Os arquivos são gerados em ./localstack-[recurso]-[nome]
cd localstack-s3-meu-bucket-teste
terraform init
terraform apply

# Testar
aws --endpoint-url=http://localhost:4566 s3 ls
```

## 📋 **Exemplo Completo: S3**

### **Passo 1: Criar Template**
1. Acesse Backstage
2. Escolha `LocalStack S3 Local - Terraform`
3. Preencha:
   - **Nome**: `meu-bucket-teste`
   - **Descrição**: `Bucket para testes`
   - **Propósito**: `data-storage`
   - **Versionamento**: `Sim`

### **Passo 2: Arquivos Gerados**
```
localstack-s3-meu-bucket-teste/
├── main.tf          # Recursos Terraform
├── outputs.tf       # Outputs com comandos úteis
└── README.md        # Documentação completa
```

### **Passo 3: Aplicar**
```bash
cd localstack-s3-meu-bucket-teste
terraform init
terraform apply
```

### **Passo 4: Testar**
```bash
# Listar buckets
aws --endpoint-url=http://localhost:4566 s3 ls

# Listar objetos (já tem arquivos de exemplo)
aws --endpoint-url=http://localhost:4566 s3 ls s3://meu-bucket-teste-[sufixo]/

# Download arquivo de exemplo
aws --endpoint-url=http://localhost:4566 s3 cp s3://meu-bucket-teste-[sufixo]/sample.txt .

# Upload seu arquivo
echo "Meu teste" > test.txt
aws --endpoint-url=http://localhost:4566 s3 cp test.txt s3://meu-bucket-teste-[sufixo]/
```

## 📊 **Exemplo Completo: DynamoDB**

### **Passo 1: Criar Template**
1. Escolha `LocalStack DynamoDB Local - Terraform`
2. Preencha:
   - **Nome**: `MinhaTabela`
   - **Hash Key**: `id`
   - **Range Key**: `timestamp` (opcional)
   - **Dados de Exemplo**: `Sim`

### **Passo 2: Aplicar**
```bash
cd localstack-dynamodb-MinhaTabela
terraform init
terraform apply
```

### **Passo 3: Testar**
```bash
# Listar tabelas
aws --endpoint-url=http://localhost:4566 dynamodb list-tables

# Scan tabela (ver dados de exemplo)
aws --endpoint-url=http://localhost:4566 dynamodb scan --table-name MinhaTabela

# Inserir novo item
aws --endpoint-url=http://localhost:4566 dynamodb put-item \
  --table-name MinhaTabela \
  --item '{"id":{"S":"meu-id"},"name":{"S":"Meu Item"},"active":{"BOOL":true}}'

# Buscar item
aws --endpoint-url=http://localhost:4566 dynamodb get-item \
  --table-name MinhaTabela \
  --key '{"id":{"S":"meu-id"}}'
```

## 🖥️ **Exemplo Completo: EC2**

### **Passo 1: Criar Template**
1. Escolha `LocalStack EC2 Local - Terraform`
2. Preencha:
   - **Nome**: `minha-instancia-teste`
   - **Tipo**: `t2.micro`
   - **IP Público**: `Sim`

### **Passo 2: Aplicar**
```bash
cd localstack-ec2-minha-instancia-teste
terraform init
terraform apply
```

### **Passo 3: Testar**
```bash
# Listar instâncias
aws --endpoint-url=http://localhost:4566 ec2 describe-instances

# Ver security groups
aws --endpoint-url=http://localhost:4566 ec2 describe-security-groups

# Ver key pairs
aws --endpoint-url=http://localhost:4566 ec2 describe-key-pairs
```

## 🎯 **Vantagens dos Templates Offline**

### ✅ **Sem Dependências Externas**
- Não precisa de GitHub
- Não precisa de tokens
- Não precisa de internet (após baixar LocalStack)

### ✅ **Desenvolvimento Rápido**
- Arquivos gerados instantaneamente
- Pronto para usar com Terraform
- Dados de exemplo incluídos

### ✅ **Documentação Automática**
- README.md gerado automaticamente
- Comandos de teste incluídos
- Exemplos de uso completos

### ✅ **Flexibilidade Total**
- Modifique arquivos como quiser
- Adicione recursos extras
- Adapte para suas necessidades

## 🔧 **Troubleshooting**

### **Template não aparece**
```bash
# Verificar configuração
grep -A 3 "localstack.*local" app-config.yaml

# Restart Backstage
yarn dev
```

### **LocalStack não funciona**
```bash
# Verificar se está rodando
docker ps | grep localstack

# Ver logs
docker logs $(docker ps -q --filter ancestor=localstack/localstack)

# Reiniciar
docker stop $(docker ps -q --filter ancestor=localstack/localstack)
docker run --rm -it -p 4566:4566 localstack/localstack
```

### **Terraform falha**
```bash
# Verificar LocalStack
curl http://localhost:4566/health

# Validar Terraform
terraform validate

# Debug
export TF_LOG=DEBUG
terraform apply
```

## 📚 **Estrutura dos Arquivos Gerados**

### **S3 Template**
```
localstack-s3-[nome]/
├── main.tf          # Bucket + objetos de exemplo
├── outputs.tf       # URLs e comandos de teste
└── README.md        # Guia completo de uso
```

### **EC2 Template**
```
localstack-ec2-[nome]/
├── main.tf          # Instância + Security Group + Key Pair
├── user_data.sh     # Script de inicialização
├── outputs.tf       # IPs e comandos de teste
└── README.md        # Guia completo de uso
```

### **DynamoDB Template**
```
localstack-dynamodb-[nome]/
├── main.tf          # Tabela + itens de exemplo
├── outputs.tf       # Comandos CRUD completos
└── README.md        # Guia completo de uso
```

## 🎉 **Conclusão**

Agora você tem **templates LocalStack completamente offline** que:

- ✅ **Funcionam sem GitHub**
- ✅ **Geram arquivos localmente**
- ✅ **Incluem dados de exemplo**
- ✅ **Têm documentação completa**
- ✅ **São prontos para usar**

**Teste primeiro o S3**, depois expanda para EC2 e DynamoDB conforme necessário! 🚀

---

**Criado para**: Desenvolvimento Local Offline  
**Tecnologias**: LocalStack + Terraform + Backstage  
**Sem dependências**: GitHub, tokens, internet
