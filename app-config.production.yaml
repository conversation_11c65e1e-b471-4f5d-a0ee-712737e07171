app:
  # Should be the same as backend.baseUrl when using the `app-backend` plugin.
  baseUrl: http://localhost:7007

backend:
  # Note that the baseUrl should be the URL that the browser and other clients
  # should use when communicating with the backend, i.e. it needs to be
  # reachable not just from within the backend host, but from all of your
  # callers. When its value is "http://localhost:7007", it's strictly private
  # and can't be reached by others.
  baseUrl: http://localhost:7007
  # The listener can also be expressed as a single <host>:<port> string. In this case we bind to
  # all interfaces, the most permissive setting. The right value depends on your specific deployment.
  listen: ':7007'

  # config options: https://node-postgres.com/apis/client
  database:
    client: pg
    connection:
      host: ${POSTGRES_HOST}
      port: ${POSTGRES_PORT}
      user: ${POSTGRES_USER}
      password: ${POSTGRES_PASSWORD}
      # https://node-postgres.com/features/ssl
      # you can set the sslmode configuration option via the `PGSSLMODE` environment variable
      # see https://www.postgresql.org/docs/current/libpq-ssl.html Table 33.1. SSL Mode Descriptions (e.g. require)
      # ssl:
      #   ca: # if you have a CA file and want to verify it you can uncomment this section
      #     $file: <file-path>/ca/server.crt

auth:
  providers:
    guest: {}

catalog:
  # Overrides the default list locations from app-config.yaml as these contain example data.
  # See https://backstage.io/docs/features/software-catalog/#adding-components-to-the-catalog for more details
  # on how to get entities into the catalog.
  locations:
    # Local example data, replace this with your production config, these are intended for demo use only.
    # File locations are relative to the backend process, typically in a deployed context, such as in a Docker container, this will be the root
    - type: file
      target: ./examples/entities.yaml

    # Local example template
    - type: file
      target: ./examples/template/template.yaml
      rules:
        - allow: [Template]

    # EKS Cluster template
    - type: file
      target: ./examples/eks-cluster/template.yaml
      rules:
        - allow: [Template]

    # EC2 Instance template
    - type: file
      target: ./examples/ec2-instance/template.yaml
      rules:
        - allow: [Template]

    # S3 Bucket template
    - type: file
      target: ./examples/s3-bucket/template.yaml
      rules:
        - allow: [Template]

    # Security Group template
    - type: file
      target: ./examples/security-group/template.yaml
      rules:
        - allow: [Template]

    # RDS Database template
    - type: file
      target: ./examples/rds-database/template.yaml
      rules:
        - allow: [Template]

    # VPC template
    - type: file
      target: ./examples/vpc/template.yaml
      rules:
        - allow: [Template]

    # Engie Templates
    - type: file
      target: ./engie/template-terraform-ec2/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ./engie/template-terraform-s3/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ./engie/template-terraform-rds/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ./engie/template-terraform-vpc/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ./engie/template-terraform-eks/template.yaml
      rules:
        - allow: [Template]

    # LocalStack Templates
    - type: file
      target: ./local/template-localstack-s3/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ./local/template-localstack-ec2/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ./local/template-localstack-dynamodb/template.yaml
      rules:
        - allow: [Template]

    # Local example organizational data
    - type: file
      target: ./examples/org.yaml
      rules:
        - allow: [User, Group]
