#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default = "us-east-1"
  type        = string
}

#PROJECT CONFIGURATION
variable "ec2_template" {
  description = "Name of the launch Template"
  default = "${{ values.name }}-template"
  type        = string
}

## LAUNCH TEMPLATE CONFIGURATION
variable "amazon_linux_2" {
  description = "Enabled Amazon Linux 2023 AMI"
  default = ${{ values.use_amazon2 }}
  type        = bool
}

variable "instance_type" {
  description = "The instance type"
  default = "${{ values.instance_type }}"
  type        = string
}

variable "device_name" {
  description = "The EBS device name"
  default = "${{ values.name }}-ebs"
  type        = string
}

variable "volume_type" {
  description = "The EBS volume type"
  default = "${{ values.volume_type }}"
  type        = string
}

variable "volume_size" {
  description = "The EBS volume size"
  default = ${{ values.volume_size }}
  type        = number
}

variable "ebs_delete_on_termination" {
  description = "Delete the EBS type on instance termination"
  default = ${{ values.ebs_delete_on_termination }}
  type        = bool
}

variable "enabled_user_data" {
  description = "Enabled/Disabled intance user data"
  default = ${{ values.enabled_user_data }}
  type        = bool
}
{% if values.enabled_user_data %}
variable "user_data" {
  description = "The absolute path and the user data file name"
  default = "${{ values.user_data }}"
  type        = string
}
{% endif %}

#NETWORK CONFIGURATION
{% if values.vpc_filter_key %}
variable "vpc_filter_key" {
  description = "VPC key tag name"
  default = "${{ values.vpc_filter_key }}"
  type        = string
}
{% endif %}

{% if values.vpc_filter_tag %}
variable "vpc_filter" {
  description = "VPC value tag"
  default = "${{ values.vpc_filter_tag }}"
  type        = string
}
{% endif %}

{% if values.private_subnet_filter_key %}
variable "private_subnet_filter_key" {
  description = "Subnet key tag name"
  default = "${{ values.private_subnet_filter_key }}"
  type        = string
}
{% endif %}

{% if values.private_subnet_filter_tag %}
variable "private_subnet_filter" {
  description = "Subnet value tag"
  default = "${{ values.private_subnet_filter_tag }}"
  type        = string
}
{% endif %}

{% if values.vpc_az != "" %}
variable "vpc_az" {
  description = "Lista de Zonas de Disponibilidade"
  default = [ ${{ values.vpc_az }} ]
  type        = list(string)
}
{% endif %}

## Security Group
variable "create_security_group" {
  description = "Create a new SG using the module"
  default = "${{values.create_sg}}"
  type        = bool
}

{% if values.ingress_ports != "" %}
variable "ingress_ports" {
  description = "Inbound SG ports"
  default = [ ${{ values.ingress_ports }} ]
  type        = list(number)
}
{% endif %}

{% if values.ingress_cidrs != "" %}
variable "ingress_cidrs" {
  description = "Inbound CIDR access"
  default = [ ${{ values.ingress_cidrs }} ]
  type        = list(string)
}
{% endif %}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
}
