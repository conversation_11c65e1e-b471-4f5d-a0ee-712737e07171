#!/bin/bash

# Atualizar os pacotes existentes
sudo yum update -y

# Instala o pacote de bibliotecas baseado em RPM
sudo yum install -y libnl3

# Instalar kubectl
sudo curl -O https://s3.us-west-2.amazonaws.com/amazon-eks/1.28.3/2023-11-14/bin/linux/amd64/kubectl
sudo chmod +x ./kubectl
sudo mkdir -p $HOME/bin && sudo cp ./kubectl $HOME/bin/kubectl && export PATH=$HOME/bin:$PATH
sudo echo 'export PATH=$HOME/bin:$PATH' >> ~/.bashrc

# Instalar terraform
sudo yum install -y unzip
sudo curl -o terraform.zip https://releases.hashicorp.com/terraform/0.15.0/terraform_0.15.0_linux_amd64.zip
sudo unzip terraform.zip
sudo mv terraform /usr/local/bin/
sudo rm terraform.zip

# Instalar ansible
sudo yum install ansible-core.x86_64 -y

# Instalar docker
sudo yum install docker -y
sudo service docker start
sudo usermod -a -G docker ec2-user

# Instalar docker compose
wget https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m) 
sudo mv docker-compose-$(uname -s)-$(uname -m) /usr/local/bin/docker-compose
sudo chmod -v +x /usr/local/bin/docker-compose
