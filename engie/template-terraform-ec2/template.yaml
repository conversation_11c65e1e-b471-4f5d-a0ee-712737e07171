apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-terraform-ec2
  title: Nova Maquina EC2 - Terraform
  description: Cria um código base em terraform para criação de maquinas EC2 na AWS
  tags:
    - ec2
    - aws
spec:
  owner: backstage
  type: aws
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome da EC2
          type: string
          description: Nome da maquina
          pattern: ^[a-zA-Z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição da Collection
        owner:
          title: GroupID
          type: string
          description: Responsável pela Collection
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
    - title: Informações da Máquina
      required:
        - instance_type
        - volume_type
        - use_amazon2
        - volume_size
      properties:
        use_amazon2:
          title: Usar o Amazon 2?
          type: boolean
          description: Utilizar o Amazon 2 como imagem?
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        instance_type:
          title: Tipo de Instância EC2
          type: string
          description: Escolha o Tipo de EC2 para criar
          examples:
            - t2.medium
            - t3.small
            - r5a.xlarge
            - t3a.medium
        volume_type:
          title: Tipo de EBS para a EC2
          type: string
          description: Escolha o Tipo de EBS para a  EC2
          default: gp3
          enum:
            - gp2
            - gp3
            - io1
            - io2
          enumNames:
            - General Purpose SSD (gp2)
            - General Purpose SSD (gp3)
            - Provisioned IOPS (io1)
            - Provisioned IOPS (io2)
        volume_size:
          type: number
          title: Tamanho do Disco (GB)
          description: Tamanho do disco da máquina
          default: 50
          pattern: ^[0-9]*$
        ebs_delete_on_termination:
          title: Deletar o Disco ao deletar a máquina?
          type: boolean
          description: Deleta o Disco ao deletar a máquina
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enabled_user_data:
          title: Rodar scripts no userdata ao criar a maquina?
          type: boolean
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
          default: false
          description: Rodar scripts no userdata ao criar a maquina?
      dependencies:
        enabled_user_data:
          oneOf:
            - properties:
                enabled_user_data:
                  enum:
                    - true
                user_data:
                  title: User Data
                  type: string
                  description: Caminho do arquivo userdata
            - properties:
                enabled_user_data:
                  enum:
                    - false
    - title: Informações de Redes
      properties:
        create_sg:
          title: Criar um novo Security Group?
          type: boolean
          description: Criar novo Security Group
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
          ui:widget: radio
        vpc_filter_key:
          title: Key da VPC
          type: string
          description: Key utilizada para buscar a VPC
          examples:
            - vpc-0ef416b78889d9295
            - vpc-096c613b72def76e1
        vpc_filter_tag:
          title: Tag da VPC
          type: string
          description: Tag utilizada para buscar a VPC
        private_subnet_filter_key:
          title: Key da Subnete
          type: string
          examples:
            - subnet-0e144201c4144094b
            - subnet-095cb4f3153ffd8a0
          description: Key utilizada para buscar a Subnet
        private_subnet_filter_tag:
          title: TAG da Subnete
          type: string
          description: TAG utilizada para buscar a Subnet
        vpc_az:
          title: Lista das A-Z
          type: array
          items:
            type: string
            examples:
              - us-east-1a
              - us-east-1b
          ui:options:
            orderable: false
          description: Listas das A-Z
        ingress_ports:
          title: Lista de portas liberadas no SG para inbound
          type: array
          items:
            type: string
            examples:
              - 9990
              - 8080
          ui:options:
            orderable: false
          description: Listas dos ips de entradas do SG
        ingress_cidrs:
          title: Lista dos cidrs do SG
          type: array
          items:
            type: string
            examples:
              - 10.0.0.0/8
          ui:options:
            orderable: false
          description: Listas das entradas do SG
    - title: Bitbucket
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repositório
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - almappro.tractebelenergia.com.br
            allowedOwners:
              - backstage
            allowedProjects:
              - BAC
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          create_sg: ${{ parameters.create_sg }}
          use_amazon2: ${{ parameters.use_amazon2 }}
          instance_type: ${{ parameters.instance_type }}
          volume_type: ${{ parameters.volume_type }}
          ebs_delete_on_termination: ${{ parameters.ebs_delete_on_termination }}
          enabled_user_data: ${{ parameters.enabled_user_data }}
          user_data: ${{ parameters.user_data }}
          vpc_filter_key: ${{ parameters.vpc_filter_key }}
          vpc_filter_tag: ${{ parameters.vpc_filter_tag }}
          private_subnet_filter_key: ${{ parameters.private_subnet_filter_key }}
          private_subnet_filter_tag: ${{ parameters.private_subnet_filter_tag }}
          vpc_az: ${{ parameters.vpc_az }}
          ingress_ports: ${{ parameters.ingress_ports }}
          ingress_cidrs: ${{ parameters.ingress_cidrs }}
          volume_size: ${{ parameters.volume_size }}
    - id: publish
      name: Publish
      action: publish:bitbucketServer
      input:
        allowedHosts:
          - almappro.tractebelenergia.com.br
        description: Componente ${{ parameters.name }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
