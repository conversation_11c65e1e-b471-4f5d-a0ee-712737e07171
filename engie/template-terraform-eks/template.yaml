apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-terraform-eks
  title: Novo Cluster EKS - Terraform
  description: Cria um código base em terraform para criação de clusters EKS na AWS
  tags:
    - eks
    - aws
    - kubernetes
spec:
  owner: backstage
  type: aws
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome do Cluster EKS
          type: string
          description: Nome do cluster Kubernetes
          pattern: ^[a-zA-Z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição do Cluster EKS
        owner:
          title: GroupID
          type: string
          description: Responsável pelo Cluster
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
    - title: Configurações do Cluster
      required:
        - kubernetes_version
        - node_group_instance_type
        - node_group_desired_size
      properties:
        kubernetes_version:
          title: Versão do Kubernetes
          type: string
          description: Versão do Kubernetes para o cluster
          default: "1.28"
          enum:
            - "1.26"
            - "1.27"
            - "1.28"
            - "1.29"
          enumNames:
            - "1.26"
            - "1.27"
            - "1.28"
            - "1.29"
        node_group_instance_type:
          title: Tipo de Instância dos Nodes
          type: string
          description: Tipo de instância para os worker nodes
          default: t3.medium
          enum:
            - t3.small
            - t3.medium
            - t3.large
            - m5.large
            - m5.xlarge
            - c5.large
            - c5.xlarge
          enumNames:
            - t3.small (2 vCPU, 2 GB RAM)
            - t3.medium (2 vCPU, 4 GB RAM)
            - t3.large (2 vCPU, 8 GB RAM)
            - m5.large (2 vCPU, 8 GB RAM)
            - m5.xlarge (4 vCPU, 16 GB RAM)
            - c5.large (2 vCPU, 4 GB RAM)
            - c5.xlarge (4 vCPU, 8 GB RAM)
        node_group_desired_size:
          title: Número Desejado de Nodes
          type: integer
          description: Número desejado de worker nodes
          default: 2
          minimum: 1
          maximum: 10
        node_group_min_size:
          title: Número Mínimo de Nodes
          type: integer
          description: Número mínimo de worker nodes
          default: 1
          minimum: 1
          maximum: 10
        node_group_max_size:
          title: Número Máximo de Nodes
          type: integer
          description: Número máximo de worker nodes
          default: 4
          minimum: 1
          maximum: 20
        enable_cluster_logging:
          title: Habilitar Logs do Cluster?
          type: boolean
          description: Habilitar logs do control plane
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_irsa:
          title: Habilitar IRSA?
          type: boolean
          description: Habilitar IAM Roles for Service Accounts
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações de Rede
      properties:
        vpc_filter_key:
          title: Key da VPC
          type: string
          description: Key utilizada para buscar a VPC
          examples:
            - vpc-0ef416b78889d9295
            - vpc-096c613b72def76e1
        vpc_filter_tag:
          title: Tag da VPC
          type: string
          description: Tag utilizada para buscar a VPC
        subnet_filter_key:
          title: Key das Subnets
          type: string
          description: Key utilizada para buscar as Subnets
          examples:
            - subnet-0e144201c4144094b
            - subnet-095cb4f3153ffd8a0
        subnet_filter_tag:
          title: Tag das Subnets
          type: string
          description: Tag utilizada para buscar as Subnets
        endpoint_private_access:
          title: Acesso Privado ao Endpoint?
          type: boolean
          description: Habilitar acesso privado ao endpoint da API
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        endpoint_public_access:
          title: Acesso Público ao Endpoint?
          type: boolean
          description: Habilitar acesso público ao endpoint da API
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações de Região
      required:
        - aws_region
      properties:
        aws_region:
          title: Região AWS
          type: string
          description: Região onde criar o cluster
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - sa-east-1
          enumNames:
            - US East (N. Virginia)
            - US West (Oregon)
            - Europe (Ireland)
            - South America (São Paulo)
    - title: Bitbucket
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repositório
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - almappro.tractebelenergia.com.br
            allowedOwners:
              - backstage
            allowedProjects:
              - BAC
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          kubernetes_version: ${{ parameters.kubernetes_version }}
          node_group_instance_type: ${{ parameters.node_group_instance_type }}
          node_group_desired_size: ${{ parameters.node_group_desired_size }}
          node_group_min_size: ${{ parameters.node_group_min_size }}
          node_group_max_size: ${{ parameters.node_group_max_size }}
          enable_cluster_logging: ${{ parameters.enable_cluster_logging }}
          enable_irsa: ${{ parameters.enable_irsa }}
          vpc_filter_key: ${{ parameters.vpc_filter_key }}
          vpc_filter_tag: ${{ parameters.vpc_filter_tag }}
          subnet_filter_key: ${{ parameters.subnet_filter_key }}
          subnet_filter_tag: ${{ parameters.subnet_filter_tag }}
          endpoint_private_access: ${{ parameters.endpoint_private_access }}
          endpoint_public_access: ${{ parameters.endpoint_public_access }}
          aws_region: ${{ parameters.aws_region }}
    - id: publish
      name: Publish
      action: publish:bitbucketServer
      input:
        allowedHosts:
          - almappro.tractebelenergia.com.br
        description: Componente ${{ parameters.name }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
