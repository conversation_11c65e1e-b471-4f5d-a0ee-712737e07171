#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default = "us-east-1"
  type        = string
}

#DATABASE CONFIGURATION
variable "db_identifier" {
  description = "Database identifier"
  default = "${{ values.name }}-db"
  type        = string
}

variable "engine" {
  description = "Database engine"
  default = "${{ values.engine }}"
  type        = string
}

variable "instance_class" {
  description = "Database instance class"
  default = "${{ values.instance_class }}"
  type        = string
}

variable "allocated_storage" {
  description = "Database allocated storage in GB"
  default = ${{ values.allocated_storage }}
  type        = number
}

variable "username" {
  description = "Database master username"
  default = "${{ values.username }}"
  type        = string
}

#HIGH AVAILABILITY
variable "multi_az" {
  description = "Enable Multi-AZ deployment"
  default = ${{ values.multi_az }}
  type        = bool
}

#BACKUP CONFIGURATION
variable "enable_backups" {
  description = "Enable automated backups"
  default = ${{ values.enable_backups }}
  type        = bool
}

{% if values.enable_backups %}
variable "backup_retention_period" {
  description = "Backup retention period in days"
  default = ${{ values.backup_retention_period }}
  type        = number
}

variable "backup_window" {
  description = "Backup window time"
  default = "${{ values.backup_window }}"
  type        = string
}
{% endif %}

#SECURITY CONFIGURATION
variable "enable_encryption" {
  description = "Enable encryption at rest"
  default = ${{ values.enable_encryption }}
  type        = bool
}

variable "publicly_accessible" {
  description = "Make database publicly accessible"
  default = ${{ values.publicly_accessible }}
  type        = bool
}

#NETWORK CONFIGURATION
{% if values.vpc_filter_key %}
variable "vpc_filter_key" {
  description = "VPC key tag name"
  default = "${{ values.vpc_filter_key }}"
  type        = string
}
{% endif %}

{% if values.vpc_filter_tag %}
variable "vpc_filter" {
  description = "VPC value tag"
  default = "${{ values.vpc_filter_tag }}"
  type        = string
}
{% endif %}

{% if values.subnet_group_name %}
variable "subnet_group_name" {
  description = "DB Subnet Group name"
  default = "${{ values.subnet_group_name }}"
  type        = string
}
{% endif %}

{% if values.allowed_security_groups != "" %}
variable "allowed_security_groups" {
  description = "List of security groups allowed to access the database"
  default = [ ${{ values.allowed_security_groups }} ]
  type        = list(string)
}
{% endif %}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "production"
    ManagedBy   = "terraform"
    Engine      = "${{ values.engine }}"
  }
}
