module "rds" {
  source = "git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-rds.git"

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## DATABASE CONFIGURATION
  db_identifier     = var.db_identifier
  engine           = var.engine
  instance_class   = var.instance_class
  allocated_storage = var.allocated_storage
  username         = var.username
  
  ## HIGH AVAILABILITY
  multi_az = var.multi_az
  
  ## BACKUP CONFIGURATION
  enable_backups           = var.enable_backups
  backup_retention_period  = var.backup_retention_period
  backup_window           = var.backup_window
  
  ## SECURITY CONFIGURATION
  enable_encryption    = var.enable_encryption
  publicly_accessible  = var.publicly_accessible
  
  ## NETWORK CONFIGURATION
  vpc_filter_key           = var.vpc_filter_key
  vpc_filter              = var.vpc_filter
  subnet_group_name       = var.subnet_group_name
  allowed_security_groups = var.allowed_security_groups

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
