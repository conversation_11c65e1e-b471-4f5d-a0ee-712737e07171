module "vpc" {
  source = "git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-vpc.git"

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## VPC CONFIGURATION
  vpc_name = var.vpc_name
  vpc_cidr = var.vpc_cidr
  
  ## AVAILABILITY ZONES
  availability_zones = var.availability_zones
  
  ## DNS CONFIGURATION
  enable_dns_hostnames = var.enable_dns_hostnames
  enable_dns_support   = var.enable_dns_support
  
  ## NAT GATEWAY CONFIGURATION
  enable_nat_gateway = var.enable_nat_gateway
  
  ## SUBNET CONFIGURATION
  create_private_subnets  = var.create_private_subnets
  create_database_subnets = var.create_database_subnets
  
  ## MONITORING CONFIGURATION
  enable_vpc_flow_logs = var.enable_vpc_flow_logs

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
