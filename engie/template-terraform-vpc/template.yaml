apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-terraform-vpc
  title: Nova VPC - Terraform
  description: Cria um código base em terraform para criação de VPCs na AWS
  tags:
    - vpc
    - aws
    - networking
spec:
  owner: backstage
  type: aws
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome da VPC
          type: string
          description: Nome da VPC
          pattern: ^[a-zA-Z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição da VPC
        owner:
          title: GroupID
          type: string
          description: Responsável pela VPC
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
    - title: Configurações de Rede
      required:
        - vpc_cidr
        - availability_zones
      properties:
        vpc_cidr:
          title: CIDR da VPC
          type: string
          description: Bloco CIDR para a VPC
          default: 10.0.0.0/16
          enum:
            - 10.0.0.0/16
            - **********/16
            - ***********/16
          enumNames:
            - 10.0.0.0/16 (65,536 IPs)
            - **********/16 (65,536 IPs)
            - ***********/16 (65,536 IPs)
        availability_zones:
          title: Número de Zonas de Disponibilidade
          type: integer
          description: Número de AZs para usar (2-3 recomendado)
          default: 2
          enum:
            - 2
            - 3
          enumNames:
            - 2 AZs
            - 3 AZs
        enable_nat_gateway:
          title: Habilitar NAT Gateway?
          type: boolean
          description: Criar NAT Gateway para subnets privadas
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_dns_hostnames:
          title: Habilitar DNS Hostnames?
          type: boolean
          description: Habilitar hostnames DNS na VPC
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_dns_support:
          title: Habilitar DNS Support?
          type: boolean
          description: Habilitar resolução DNS na VPC
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_vpc_flow_logs:
          title: Habilitar VPC Flow Logs?
          type: boolean
          description: Habilitar logs de fluxo da VPC
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações de Subnets
      properties:
        create_private_subnets:
          title: Criar Subnets Privadas?
          type: boolean
          description: Criar subnets privadas além das públicas
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        create_database_subnets:
          title: Criar Subnets de Banco?
          type: boolean
          description: Criar subnets dedicadas para bancos de dados
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
    - title: Configurações de Região
      required:
        - aws_region
      properties:
        aws_region:
          title: Região AWS
          type: string
          description: Região onde criar a VPC
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - sa-east-1
          enumNames:
            - US East (N. Virginia)
            - US West (Oregon)
            - Europe (Ireland)
            - South America (São Paulo)
    - title: Bitbucket
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repositório
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - almappro.tractebelenergia.com.br
            allowedOwners:
              - backstage
            allowedProjects:
              - BAC
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          vpc_cidr: ${{ parameters.vpc_cidr }}
          availability_zones: ${{ parameters.availability_zones }}
          enable_nat_gateway: ${{ parameters.enable_nat_gateway }}
          enable_dns_hostnames: ${{ parameters.enable_dns_hostnames }}
          enable_dns_support: ${{ parameters.enable_dns_support }}
          enable_vpc_flow_logs: ${{ parameters.enable_vpc_flow_logs }}
          create_private_subnets: ${{ parameters.create_private_subnets }}
          create_database_subnets: ${{ parameters.create_database_subnets }}
          aws_region: ${{ parameters.aws_region }}
    - id: publish
      name: Publish
      action: publish:bitbucketServer
      input:
        allowedHosts:
          - almappro.tractebelenergia.com.br
        description: Componente ${{ parameters.name }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
