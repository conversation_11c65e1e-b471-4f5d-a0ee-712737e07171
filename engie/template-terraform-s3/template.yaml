apiVersion: scaffolder.backstage.io/v1beta3
kind: Template
metadata:
  name: template-terraform-s3
  title: Novo Bucket S3 - Terraform
  description: Cria um código base em terraform para criação de buckets S3 na AWS
  tags:
    - s3
    - aws
    - storage
spec:
  owner: backstage
  type: aws
  parameters:
    - title: Informações básicas
      required:
        - name
        - description
        - owner
      properties:
        name:
          title: Nome do Bucket S3
          type: string
          description: Nome do bucket (será adicionado sufixo único)
          pattern: ^[a-z0-9\-]*$
          maxLength: 50
          ui:autofocus: true
        description:
          title: Descrição
          type: string
          description: Descrição do Bucket S3
        owner:
          title: GroupID
          type: string
          description: Responsável pelo Bucket
          ui:field: OwnerPicker
          ui:options:
            allowedKinds:
              - Group
    - title: Configurações do Bucket
      required:
        - bucket_purpose
        - enable_versioning
        - enable_encryption
      properties:
        bucket_purpose:
          title: Propósito do Bucket
          type: string
          description: Finalidade principal do bucket
          default: data-storage
          enum:
            - data-storage
            - static-website
            - backup
            - logs
            - media-assets
          enumNames:
            - Armazenamento de Dados
            - Site Estático
            - Backup
            - Logs
            - Assets de Mídia
        enable_versioning:
          title: Habilitar Versionamento?
          type: boolean
          description: Habilitar versionamento de objetos
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_encryption:
          title: Habilitar Criptografia?
          type: boolean
          description: Habilitar criptografia server-side
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_public_access:
          title: Permitir Acesso Público?
          type: boolean
          description: Permitir acesso público de leitura
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        lifecycle_enabled:
          title: Habilitar Lifecycle?
          type: boolean
          description: Habilitar regras de lifecycle automático
          ui:widget: radio
          default: true
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
        enable_logging:
          title: Habilitar Logs de Acesso?
          type: boolean
          description: Habilitar logs de acesso ao bucket
          ui:widget: radio
          default: false
          enum:
            - false
            - true
          enumNames:
            - Não
            - Sim
      dependencies:
        enable_logging:
          oneOf:
            - properties:
                enable_logging:
                  enum:
                    - true
                log_bucket_name:
                  title: Nome do Bucket de Logs
                  type: string
                  description: Nome do bucket onde armazenar os logs
            - properties:
                enable_logging:
                  enum:
                    - false
    - title: Configurações de Região
      required:
        - aws_region
      properties:
        aws_region:
          title: Região AWS
          type: string
          description: Região onde criar o bucket
          default: us-east-1
          enum:
            - us-east-1
            - us-west-2
            - eu-west-1
            - sa-east-1
          enumNames:
            - US East (N. Virginia)
            - US West (Oregon)
            - Europe (Ireland)
            - South America (São Paulo)
    - title: Bitbucket
      required:
        - repoUrl
      properties:
        repoUrl:
          title: Repositório
          type: string
          ui:field: RepoUrlPicker
          ui:options:
            allowedHosts:
              - almappro.tractebelenergia.com.br
            allowedOwners:
              - backstage
            allowedProjects:
              - BAC
  steps:
    - id: template
      name: Template
      action: fetch:template
      input:
        url: ./skeleton
        copyWithoutRender:
          - .github/workflows/*
        values:
          name: ${{ parameters.name }}
          description: ${{ parameters.description }}
          destination: ${{ parameters.repoUrl | parseRepoUrl }}
          owner: ${{ parameters.owner }}
          bucket_purpose: ${{ parameters.bucket_purpose }}
          enable_versioning: ${{ parameters.enable_versioning }}
          enable_encryption: ${{ parameters.enable_encryption }}
          enable_public_access: ${{ parameters.enable_public_access }}
          lifecycle_enabled: ${{ parameters.lifecycle_enabled }}
          enable_logging: ${{ parameters.enable_logging }}
          log_bucket_name: ${{ parameters.log_bucket_name }}
          aws_region: ${{ parameters.aws_region }}
    - id: publish
      name: Publish
      action: publish:bitbucketServer
      input:
        allowedHosts:
          - almappro.tractebelenergia.com.br
        description: Componente ${{ parameters.name }}
        repoUrl: ${{ parameters.repoUrl }}
    - id: register
      name: Register
      action: catalog:register
      input:
        repoContentsUrl: ${{ steps.publish.output.repoContentsUrl }}
        catalogInfoPath: /catalog-info.yaml
  output:
    links:
      - title: Repository
        url: ${{ steps.publish.output.remoteUrl }}
      - title: Open in catalog
        icon: catalog
        entityRef: ${{ steps.register.output.entityRef }}
