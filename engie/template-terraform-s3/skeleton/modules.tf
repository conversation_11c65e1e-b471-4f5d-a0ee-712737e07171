module "s3" {
  source = "git::http://almappro.tractebelenergia.com.br/bitbucket/scm/ect/iac-terraform-aws-s3.git"

  ## PROVIDER CONFIGURATION
  aws_region = var.aws_region

  ## BUCKET CONFIGURATION
  bucket_name_prefix = var.bucket_name_prefix
  bucket_purpose     = var.bucket_purpose
  
  ## SECURITY CONFIGURATION
  enable_versioning     = var.enable_versioning
  enable_encryption     = var.enable_encryption
  enable_public_access  = var.enable_public_access
  
  ## LIFECYCLE CONFIGURATION
  lifecycle_enabled = var.lifecycle_enabled
  
  ## LOGGING CONFIGURATION
  enable_logging    = var.enable_logging
  log_bucket_name   = var.log_bucket_name

  ## RESOURCE TAGS
  resource_tags = var.resource_tags
}
