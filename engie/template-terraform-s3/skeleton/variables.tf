#PROVIDER CONFIGURATION
variable "aws_region" {
  description = "A região da AWS onde seus recursos serão implantados"
  default = "${{ values.aws_region }}"
  type        = string
}

#BUCKET CONFIGURATION
variable "bucket_name_prefix" {
  description = "Prefix for the S3 bucket name"
  default = "${{ values.name }}"
  type        = string
}

variable "bucket_purpose" {
  description = "Purpose of the S3 bucket"
  default = "${{ values.bucket_purpose }}"
  type        = string
}

#SECURITY CONFIGURATION
variable "enable_versioning" {
  description = "Enable S3 bucket versioning"
  default = ${{ values.enable_versioning }}
  type        = bool
}

variable "enable_encryption" {
  description = "Enable S3 bucket encryption"
  default = ${{ values.enable_encryption }}
  type        = bool
}

variable "enable_public_access" {
  description = "Enable public access to the bucket"
  default = ${{ values.enable_public_access }}
  type        = bool
}

#LIFECYCLE CONFIGURATION
variable "lifecycle_enabled" {
  description = "Enable lifecycle management"
  default = ${{ values.lifecycle_enabled }}
  type        = bool
}

#LOGGING CONFIGURATION
variable "enable_logging" {
  description = "Enable access logging"
  default = ${{ values.enable_logging }}
  type        = bool
}

{% if values.enable_logging %}
variable "log_bucket_name" {
  description = "Name of the bucket to store access logs"
  default = "${{ values.log_bucket_name }}"
  type        = string
}
{% endif %}

#RESOURCE TAGS
variable "resource_tags" {
  description = "Tags to set for all resources"
  type        = map(string)
  default = {
    Environment = "production"
    ManagedBy   = "terraform"
    Purpose     = "${{ values.bucket_purpose }}"
  }
}
