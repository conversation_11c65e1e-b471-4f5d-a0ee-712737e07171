app:
  title: Scaffolded Backstage App
  baseUrl: http://localhost:3000

organization:
  name: My Company

backend:
  # Used for enabling authentication, secret is shared by all backend plugins
  # See https://backstage.io/docs/auth/service-to-service-auth for
  # information on the format
  # auth:
  #   keys:
  #     - secret: ${BACKEND_SECRET}
  baseUrl: http://localhost:7007
  listen:
    port: 7007
    # Uncomment the following host directive to bind to specific interfaces
    # host: 127.0.0.1
  csp:
    connect-src: ["'self'", 'http:', 'https:']
    # Content-Security-Policy directives follow the Helmet format: https://helmetjs.github.io/#reference
    # Default Helmet Content-Security-Policy values can be removed by setting the key to false
  cors:
    origin: http://localhost:3000
    methods: [GET, HEAD, PATCH, POST, PUT, DELETE]
    credentials: true
  # This is for local development only, it is not recommended to use this in production
  # The production database configuration is stored in app-config.production.yaml
  database:
    client: better-sqlite3
    connection: ':memory:'
  # workingDirectory: /tmp # Use this to configure a working directory for the scaffolder, defaults to the OS temp-dir

integrations:
  github:
    - host: github.com
      # This is a Personal Access Token or PAT from GitHub. You can find out how to generate this token, and more information
      # about setting up the GitHub integration here: https://backstage.io/docs/integrations/github/locations#configuration
      token: ${GITHUB_TOKEN}
    ### Example for how to add your GitHub Enterprise instance using the API:
    # - host: ghe.example.net
    #   apiBaseUrl: https://ghe.example.net/api/v3
    #   token: ${GHE_TOKEN}

proxy:
  ### Example for how to add a proxy endpoint for the frontend.
  ### A typical reason to do this is to handle HTTPS and CORS for internal services.
  # endpoints:
  #   '/test':
  #     target: 'https://example.com'
  #     changeOrigin: true

# Reference documentation http://backstage.io/docs/features/techdocs/configuration
# Note: After experimenting with basic setup, use CI/CD to generate docs
# and an external cloud storage when deploying TechDocs for production use-case.
# https://backstage.io/docs/features/techdocs/how-to-guides#how-to-migrate-from-techdocs-basic-to-recommended-deployment-approach
techdocs:
  builder: 'local' # Alternatives - 'external'
  generator:
    runIn: 'docker' # Alternatives - 'local'
  publisher:
    type: 'local' # Alternatives - 'googleGcs' or 'awsS3'. Read documentation for using alternatives.

auth:
  # see https://backstage.io/docs/auth/ to learn about auth providers
  providers:
    # See https://backstage.io/docs/auth/guest/provider
    guest: {}

scaffolder:
  # see https://backstage.io/docs/features/software-templates/configuration for software template options

catalog:
  import:
    entityFilename: catalog-info.yaml
    pullRequestBranchName: backstage-integration
  rules:
    - allow: [Component, System, API, Resource, Location]
  locations:
    # Local example data, file locations are relative to the backend process, typically `packages/backend`
    - type: file
      target: ../../examples/entities.yaml

    # Local example template
    - type: file
      target: ../../examples/template/template.yaml
      rules:
        - allow: [Template]

    # EKS Cluster template
    - type: file
      target: ../../examples/eks-cluster/template.yaml
      rules:
        - allow: [Template]

    # EC2 Instance template
    - type: file
      target: ../../examples/ec2-instance/template.yaml
      rules:
        - allow: [Template]

    # S3 Bucket template
    - type: file
      target: ../../examples/s3-bucket/template.yaml
      rules:
        - allow: [Template]

    # Security Group template
    - type: file
      target: ../../examples/security-group/template.yaml
      rules:
        - allow: [Template]

    # RDS Database template
    - type: file
      target: ../../examples/rds-database/template.yaml
      rules:
        - allow: [Template]

    # VPC template
    - type: file
      target: ../../examples/vpc/template.yaml
      rules:
        - allow: [Template]

    # Engie Templates
    - type: file
      target: ../../engie/template-terraform-ec2/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../engie/template-terraform-s3/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../engie/template-terraform-rds/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../engie/template-terraform-vpc/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../engie/template-terraform-eks/template.yaml
      rules:
        - allow: [Template]

    # LocalStack Templates (Local - Sem GitHub)
    - type: file
      target: ../../local/template-localstack-s3-local/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../local/template-localstack-ec2-local/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../local/template-localstack-dynamodb-local/template.yaml
      rules:
        - allow: [Template]

    # LocalStack Templates (GitHub)
    - type: file
      target: ../../local/template-localstack-s3-simple/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../local/template-localstack-s3/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../local/template-localstack-ec2/template.yaml
      rules:
        - allow: [Template]

    - type: file
      target: ../../local/template-localstack-dynamodb/template.yaml
      rules:
        - allow: [Template]

    # Local example organizational data
    - type: file
      target: ../../examples/org.yaml
      rules:
        - allow: [User, Group]

    - type: file
      target: ../../ec2-catalog/template/template.yaml
      rules:
        - allow: [Template]

    ## Uncomment these lines to add more example data
    # - type: url
    #   target: https://github.com/backstage/backstage/blob/master/packages/catalog-model/examples/all.yaml

    ## Uncomment these lines to add an example org
    # - type: url
    #   target: https://github.com/backstage/backstage/blob/master/packages/catalog-model/examples/acme-corp.yaml
    #   rules:
    #     - allow: [User, Group]
  # Experimental: Always use the search method in UrlReaderProcessor.
  # New adopters are encouraged to enable it as this behavior will be the default in a future release.
  useUrlReadersSearch: true

kubernetes:
  # see https://backstage.io/docs/features/kubernetes/configuration for kubernetes configuration options

# see https://backstage.io/docs/permissions/getting-started for more on the permission framework
permission:
  # setting this to `false` will disable permissions
  enabled: true
